<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.manage.sync.mapper.TransExtendFlowSyncMapper">
    <resultMap id="BaseResultMap" type="com.pay.manage.sync.entity.TransExtendFlowEntity">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optimistic"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="FLOW_ID" jdbcType="VARCHAR" property="flowId"/>
        <result column="CUSTOMER_NO" jdbcType="VARCHAR" property="customerNo"/>
        <result column="TRANS_TIME" jdbcType="TIMESTAMP" property="transTime"/>
        <result column="SETTLE_DATE" jdbcType="VARCHAR" property="settleDate"/>
        <result column="TRANS_TYPE" jdbcType="VARCHAR" property="transType"/>
        <result column="TRANS_WAY" jdbcType="VARCHAR" property="transWay"/>
        <result column="TRANS_STATUS" jdbcType="VARCHAR" property="transStatus"/>
        <result column="SETTLE_STATUS" jdbcType="VARCHAR" property="settleStatus"/>
        <result column="POS_SN" jdbcType="VARCHAR" property="posSn"/>
        <result column="POS_CATI" jdbcType="VARCHAR" property="posCati"/>
        <result column="CARD_TYPE" jdbcType="VARCHAR" property="cardType"/>
        <result column="PAN" jdbcType="VARCHAR" property="pan"/>
        <result column="AMOUNT" jdbcType="DECIMAL" property="amount"/>
        <result column="SETTLE_AMOUNT" jdbcType="DECIMAL" property="settleAmount"/>
        <result column="SETTLE_CYCLE" jdbcType="VARCHAR" property="settleCycle"/>
        <result column="AGENT_NO" jdbcType="VARCHAR" property="agentNo"/>
        <result column="MID" jdbcType="VARCHAR" property="mid"/>
        <result column="POS_TYPE" jdbcType="VARCHAR" property="posType"/>
        <result column="CUST_FEE" jdbcType="DECIMAL" property="custFee"/>
        <result column="FEE_RATE" jdbcType="DECIMAL" property="feeRate"/>
        <result column="SIM_CARD_FEE" jdbcType="DECIMAL" property="simCardFee"/>
        <result column="MARKETING_FEE" jdbcType="DECIMAL" property="marketingFee"/>
        <result column="TOTAL_FEE" jdbcType="DECIMAL" property="totalFee"/>
        <result column="NOTIFY_STATUS" jdbcType="VARCHAR" property="notifyStatus"/>
        <result column="CUST_FEE_MODEL" jdbcType="VARCHAR" property="custFeeModel"/>
        <result column="CUST_FEE_COST" jdbcType="DECIMAL" property="custFeeCost"/>
        <result column="TOTAL_COST" jdbcType="DECIMAL" property="totalCost"/>
        <result column="TOTAL_EARN" jdbcType="DECIMAL" property="totalEarn"/>
        <result column="RETRY_NUM" jdbcType="DECIMAL" property="retryNum"/>
        <result column="SETTLE_CARD" jdbcType="VARCHAR" property="settleCard"/>
        <result column="FEE_RECORD" jdbcType="VARCHAR" property="feeRecord"/>
        <result column="FEE_TRANS_WAY" jdbcType="VARCHAR" property="feeTransWay"/>
        <result column="CHANNEL_FLOW_ID" jdbcType="VARCHAR" property="channelFlowId"/>
        <result column="TRANS_EARN" jdbcType="DECIMAL" property="transEarn"/>
        <result column="MARKETING_EARN" jdbcType="DECIMAL" property="marketingEarn"/>
        <result column="FIRST_TRANS" jdbcType="VARCHAR" property="firstTrans"/>
        <result column="POLICY_ID" jdbcType="VARCHAR" property="policyId"/>
        <result column="TRANS_CODE" jdbcType="VARCHAR" property="transCode"/>
        <result column="TRANS_DESC" jdbcType="VARCHAR" property="transDesc"/>
        <result column="ACTIVE_AMOUNT" jdbcType="DECIMAL" property="activeAmount"/>
        <result column="ACTIVE_TYPE" jdbcType="VARCHAR" property="activeType"/>
        <result column="STATION_INFO" jdbcType="VARCHAR" property="stationInfo"/>
        <result column="RISK_RECORD" jdbcType="VARCHAR" property="riskRecord"/>
        <result column="ICCID" jdbcType="VARCHAR" property="iccid"/>
        <result column="BRAND" jdbcType="VARCHAR" property="brand"/>
        <result column="CHANNEL_CUSTOMER_NAME" jdbcType="VARCHAR" property="channelCustomerName"/>
        <result column="CHANNEL_CUSTOMER_NO" jdbcType="VARCHAR" property="channelCustomerNo"/>
        <result column="CONTACT_TYPE" jdbcType="VARCHAR" property="contactType"/>
        <result column="COMPLETE_TIME" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode"/>
        <result column="ISSUER" jdbcType="VARCHAR" property="issuer"/>
        <result column="REMIT_MSG" jdbcType="VARCHAR" property="remitMsg"/>
        <result column="REMIT_TIME" jdbcType="VARCHAR" property="remitTime"/>
        <result column="OPTIMISTIC_SYNC" jdbcType="DECIMAL" property="optimisticSync"/>
        <result column="COST_RECORD" jdbcType="VARCHAR" property="costRecord"/>
        <result column="OPEN_ID" jdbcType="VARCHAR" property="openId"/>
        <result column="ORIGIN" jdbcType="VARCHAR" property="origin"/>
        <result column="REFUND_CHANNEL_FLOW_ID" jdbcType="VARCHAR" property="refundChannelFlowId"/>
        <result column="REFUND_FLOW_ID" jdbcType="VARCHAR" property="refundFlowId"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID
        , OPTIMISTIC, CREATE_TIME, UPDATE_TIME, FLOW_ID, CUSTOMER_NO, TRANS_TIME, SETTLE_DATE,
    TRANS_TYPE, TRANS_WAY, TRANS_STATUS, SETTLE_STATUS, POS_SN, POS_CATI, CARD_TYPE,
    PAN, AMOUNT, SETTLE_AMOUNT, SETTLE_CYCLE, AGENT_NO, MID, POS_TYPE, CUST_FEE, FEE_RATE,
    SIM_CARD_FEE, MARKETING_FEE, TOTAL_FEE, NOTIFY_STATUS, CUST_FEE_MODEL, CUST_FEE_COST,
    TOTAL_COST, TOTAL_EARN, RETRY_NUM, SETTLE_CARD, FEE_RECORD, FEE_TRANS_WAY, CHANNEL_FLOW_ID, TRANS_EARN,
    MARKETING_EARN, FIRST_TRANS, POLICY_ID, TRANS_CODE, TRANS_DESC, ACTIVE_AMOUNT, ACTIVE_TYPE,
    STATION_INFO, RISK_RECORD, ICCID, BRAND, CHANNEL_CUSTOMER_NAME, CHANNEL_CUSTOMER_NO,
    CONTACT_TYPE, COMPLETE_TIME, BANK_CODE, ISSUER, REMIT_MSG, REMIT_TIME, OPTIMISTIC_SYNC,
    COST_RECORD,THIRD_CUSTOMER_NO,OPEN_ID,ORIGIN,REFUND_CHANNEL_FLOW_ID,REFUND_FLOW_ID
    </sql>

    <insert id="insert" parameterType="com.pay.manage.sync.entity.TransExtendFlowEntity">
        <selectKey resultType="java.lang.Long" order="BEFORE" keyProperty="id">
            SELECT ZF_OPER_ADM.SEQ_TRANS_EXTEND_FLOW_ID.NEXTVAL FROM DUAL
        </selectKey>
        insert into ZF_OPER_ADM.TRANS_EXTEND_FLOW (ID,
        OPTIMISTIC, CREATE_TIME, UPDATE_TIME,
        FLOW_ID, CUSTOMER_NO, TRANS_TIME,
        SETTLE_DATE, TRANS_TYPE, TRANS_WAY,
        TRANS_STATUS, SETTLE_STATUS, POS_SN,
        POS_CATI, CARD_TYPE, PAN,
        AMOUNT, SETTLE_AMOUNT, SETTLE_CYCLE,
        AGENT_NO, MID, POS_TYPE,
        CUST_FEE, FEE_RATE, SIM_CARD_FEE,
        MARKETING_FEE, TOTAL_FEE, NOTIFY_STATUS,
        CUST_FEE_MODEL, CUST_FEE_COST, TOTAL_COST,
        TOTAL_EARN, RETRY_NUM, SETTLE_CARD,
        FEE_RECORD, FEE_TRANS_WAY, CHANNEL_FLOW_ID, TRANS_EARN,
        MARKETING_EARN, FIRST_TRANS, POLICY_ID,
        TRANS_CODE, TRANS_DESC, ACTIVE_AMOUNT,
        ACTIVE_TYPE, STATION_INFO, RISK_RECORD,
        ICCID, BRAND, CHANNEL_CUSTOMER_NAME,
        CHANNEL_CUSTOMER_NO, CONTACT_TYPE, COMPLETE_TIME,
        BANK_CODE, ISSUER, REMIT_MSG,
        REMIT_TIME, OPTIMISTIC_SYNC, COST_RECORD,
        THIRD_CUSTOMER_NO, OPEN_ID,ORIGIN,REFUND_CHANNEL_FLOW_ID,REFUND_FLOW_ID
        )
        values (#{id,jdbcType=DECIMAL},
        #{optimistic,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
        #{flowId,jdbcType=VARCHAR}, #{customerNo,jdbcType=VARCHAR}, #{transTime,jdbcType=TIMESTAMP},
        #{settleDate,jdbcType=VARCHAR}, #{transType,jdbcType=VARCHAR}, #{transWay,jdbcType=VARCHAR},
        #{transStatus,jdbcType=VARCHAR}, #{settleStatus,jdbcType=VARCHAR}, #{posSn,jdbcType=VARCHAR},
        #{posCati,jdbcType=VARCHAR}, #{cardType,jdbcType=VARCHAR}, #{pan,jdbcType=VARCHAR},
        #{amount,jdbcType=DECIMAL}, #{settleAmount,jdbcType=DECIMAL}, #{settleCycle,jdbcType=VARCHAR},
        #{agentNo,jdbcType=VARCHAR}, #{mid,jdbcType=VARCHAR}, #{posType,jdbcType=VARCHAR},
        #{custFee,jdbcType=DECIMAL}, #{feeRate,jdbcType=DECIMAL}, #{simCardFee,jdbcType=DECIMAL},
        #{marketingFee,jdbcType=DECIMAL}, #{totalFee,jdbcType=DECIMAL}, #{notifyStatus,jdbcType=VARCHAR},
        #{custFeeModel,jdbcType=VARCHAR}, #{custFeeCost,jdbcType=DECIMAL}, #{totalCost,jdbcType=DECIMAL},
        #{totalEarn,jdbcType=DECIMAL}, #{retryNum,jdbcType=DECIMAL}, #{settleCard,jdbcType=VARCHAR},
        #{feeRecord,jdbcType=VARCHAR},#{feeTransWay,jdbcType=VARCHAR}, #{channelFlowId,jdbcType=VARCHAR},
        #{transEarn,jdbcType=DECIMAL},
        #{marketingEarn,jdbcType=DECIMAL}, #{firstTrans,jdbcType=VARCHAR}, #{policyId,jdbcType=VARCHAR},
        #{transCode,jdbcType=VARCHAR}, #{transDesc,jdbcType=VARCHAR}, #{activeAmount,jdbcType=DECIMAL},
        #{activeType,jdbcType=VARCHAR}, #{stationInfo,jdbcType=VARCHAR}, #{riskRecord,jdbcType=VARCHAR},
        #{iccid,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{channelCustomerName,jdbcType=VARCHAR},
        #{channelCustomerNo,jdbcType=VARCHAR}, #{contactType,jdbcType=VARCHAR}, #{completeTime,jdbcType=TIMESTAMP},
        #{bankCode,jdbcType=VARCHAR}, #{issuer,jdbcType=VARCHAR}, #{remitMsg,jdbcType=VARCHAR},
        #{remitTime,jdbcType=VARCHAR}, #{optimisticSync,jdbcType=DECIMAL}, #{costRecord,jdbcType=VARCHAR},
        #{thirdCustomerNo,jdbcType=VARCHAR},#{openId,jdbcType=VARCHAR},#{origin,jdbcType=VARCHAR},
        #{refundChannelFlowId,jdbcType=VARCHAR},
        #{refundFlowId,jdbcType=VARCHAR}
        )
    </insert>

    <select id="findByFlowId" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ZF_OPER_ADM.TRANS_EXTEND_FLOW
        where FLOW_ID = #{flowId,jdbcType=VARCHAR}
    </select>
    <select id="getSequence" resultType="java.lang.Long" parameterType="java.lang.String">
        select ZF_OPER_ADM.${sequenceName}.NEXTVAL
        FROM DUAL
    </select>
    <select id="findbyFlowIdForUpdate" resultType="com.pay.manage.sync.entity.TransExtendFlowEntity">
        select
        <include refid="Base_Column_List"/>
        from ZF_OPER_ADM.TRANS_EXTEND_FLOW
        where FLOW_ID = #{flowId,jdbcType=VARCHAR}
        for update
    </select>
    <select id="findByChannelFlowId" resultType="com.pay.manage.sync.entity.TransExtendFlowEntity">
        select
        <include refid="Base_Column_List"/>
        from ZF_OPER_ADM.TRANS_EXTEND_FLOW
        where CHANNEL_FLOW_ID = #{channelFlowId,jdbcType=VARCHAR}
    </select>

    <update id="updateTransByIdForSync" parameterType="com.pay.manage.sync.entity.TransExtendFlowEntity">
        UPDATE ZF_OPER_ADM.TRANS_EXTEND_FLOW
        SET OPTIMISTIC             = OPTIMISTIC + 1,
            OPTIMISTIC_SYNC        = #{optimisticSync,jdbcType=DECIMAL},
            CHANNEL_FLOW_ID        = #{channelFlowId,jdbcType=VARCHAR},
            TRANS_STATUS           = #{transStatus,jdbcType=VARCHAR},
            UPDATE_TIME            = #{updateTime,jdbcType=TIMESTAMP},
            FEE_RECORD             = #{feeRecord,jdbcType=VARCHAR},
            FEE_TRANS_WAY          = #{feeTransWay,jdbcType=VARCHAR},
            CARD_TYPE              = #{cardType,jdbcType=VARCHAR},
            FIRST_TRANS            = #{firstTrans,jdbcType=VARCHAR},
            SETTLE_CYCLE           = #{settleCycle,jdbcType=VARCHAR},
            MID                    = #{mid,jdbcType=VARCHAR},
            ACTIVE_AMOUNT          = #{activeAmount,jdbcType=DECIMAL},
            ACTIVE_TYPE            = #{activeType,jdbcType=VARCHAR},
            TRANS_CODE             = #{transCode,jdbcType=VARCHAR},
            TRANS_DESC             = #{transDesc,jdbcType=VARCHAR},
            STATION_INFO           = #{stationInfo,jdbcType=VARCHAR},
            RISK_RECORD            = #{riskRecord,jdbcType=VARCHAR},
            ICCID                  = #{iccid,jdbcType=VARCHAR},
            BRAND                  = #{brand,jdbcType=VARCHAR},
            COMPLETE_TIME          = #{completeTime,jdbcType=TIMESTAMP},
            ISSUER                 = #{issuer,jdbcType=VARCHAR},
            CHANNEL_CUSTOMER_NAME  = #{channelCustomerName,jdbcType=VARCHAR},
            CHANNEL_CUSTOMER_NO    = #{channelCustomerNo,jdbcType=VARCHAR},
            OPEN_ID                = #{openId,jdbcType=VARCHAR},
            REFUND_CHANNEL_FLOW_ID = #{refundChannelFlowId,jdbcType=VARCHAR}
        WHERE FLOW_ID = #{flowId,jdbcType=VARCHAR}
          AND OPTIMISTIC = #{optimistic,jdbcType=DECIMAL}
          AND OPTIMISTIC_SYNC &lt; #{optimisticSync,jdbcType=DECIMAL}
    </update>

    <update id="updateLklSmSyncByFlowId" parameterType="com.pay.manage.sync.entity.TransExtendFlowEntity">
        UPDATE ZF_OPER_ADM.TRANS_EXTEND_FLOW
        SET OPTIMISTIC = OPTIMISTIC + 1,
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        <if test="custFee != null">
            , CUST_FEE = #{custFee,jdbcType=DECIMAL}
        </if>
        <if test="feeRate != null">
            , FEE_RATE = #{feeRate,jdbcType=DECIMAL}
        </if>
        <if test="dbzsFee != null">
            , DBZS_FEE = #{dbzsFee,jdbcType=DECIMAL}
        </if>
        <if test="transStatus != null">
            , TRANS_STATUS = #{transStatus,jdbcType=VARCHAR}
        </if>
        <if test="completeTime != null">
            , COMPLETE_TIME = #{completeTime,jdbcType=TIMESTAMP}
        </if>
        <if test="cardType != null">
            , CARD_TYPE = #{cardType,jdbcType=VARCHAR}
        </if>
        <if test="bankCode != null">
            , BANK_CODE = #{bankCode,jdbcType=VARCHAR}
        </if>
        WHERE FLOW_ID = #{flowId,jdbcType=VARCHAR}
        AND OPTIMISTIC = #{optimistic,jdbcType=DECIMAL}
    </update>
</mapper>