package com.pay.manage.sync.service;

import com.pay.frame.common.base.exception.OptimisticException;
import com.pay.manage.sync.entity.TransExtendFlowEntity;
import com.pay.manage.sync.mapper.TransExtendFlowSyncMapper;
import com.pay.manage.sync.mq.sync.LklSmTransFeeNotifyDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * LKLSM交易手续费同步Service
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Slf4j
@Service
public class LklSmTransFeeSyncService {

    @Resource
    private TransExtendFlowSyncMapper transExtendFlowSyncMapper;

    @Transactional(rollbackFor = Exception.class)
    public void syncTransFee(LklSmTransFeeNotifyDto transFee) {
        if (transFee == null) {
            throw new OptimisticException("PC_TRANS_FEE_LKLSM_TOPIC，数据同步异常，数据为空异常");
        }
        log.info("LKLSM交易手续费同步处理: orderNo={}, agencyNo={}, orgCode={}, fee={}, rate={}",
                transFee.getOrderNo(), transFee.getAgencyNo(), transFee.getOrgCode(),
                transFee.getFee(), transFee.getRate());

        // 1. 根据orderNo查询是否已存在记录
        TransExtendFlowEntity transExtendFlow = transExtendFlowSyncMapper.findByChannelFlowId(transFee.getOrderNo());
        if (transExtendFlow == null) {
            throw new OptimisticException("PC_TRANS_FEE_LKLSM_TOPIC，数据同步异常，数据不存在异常");
        }
        updateFee(transFee, transExtendFlow);
    }

    /**
     * 根据订单编号更新手续费
     *
     * @param updateDto 更新DTO，包含订单编号、手续费和费率
     * @return 更新结果，true表示成功，false表示失败
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFee(LklSmTransFeeNotifyDto updateDto, TransExtendFlowEntity transFlow) {
        // 1. 根据订单编号查询交易记录
        TransExtendFlowEntity updateEntity = transExtendFlowSyncMapper.findbyFlowIdForUpdate(transFlow.getFlowId());
        if (updateEntity == null) {
            throw new OptimisticException("订单编号不存在");
        }
        // 设置手续费
        if (updateDto.getFee() != null) {
            BigDecimal custFee = new BigDecimal(updateDto.getFee()).multiply(new BigDecimal("100"));
            updateEntity.setCustFee(custFee);
        }
        if (updateDto.getRate() != null) {
            BigDecimal feeRate = new BigDecimal(updateDto.getRate());
            updateEntity.setFeeRate(feeRate);
        }
        // 设置更新时间
        updateEntity.setUpdateTime(new java.util.Date());

        int updateCount = transExtendFlowSyncMapper.updateLklSmSyncByFlowId(updateEntity);
        if (updateCount != 1) {
            throw new OptimisticException("手续费更新失败，影响行数不为1: orderNo=" + updateDto.getOrderNo());
        }
    }
}
