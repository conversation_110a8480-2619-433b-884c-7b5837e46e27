package com.pay.manage.sync.service;

import com.alibaba.fastjson.JSON;
import com.pay.frame.common.base.exception.OptimisticException;
import com.pay.manage.sync.entity.TransExtendFlowEntity;
import com.pay.manage.sync.mapper.TransExtendFlowSyncMapper;
import com.pay.manage.sync.mq.sync.LklSmTransFeeNotifyDto;
import com.pay.manage.sync.mq.sync.LklSmTransNotifyDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * LKLSM交易手续费同步Service
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Slf4j
@Service
public class LklSmTransSyncService {

    @Resource
    private TransExtendFlowSyncMapper transExtendFlowSyncMapper;


    @Transactional(rollbackFor = Exception.class)
    public void syncTrans(LklSmTransNotifyDto transNotify) {
        if (transNotify == null) {
            throw new OptimisticException("PC_TRANS_LKLSM_TOPIC，数据同步异常，数据为空异常");
        }
        log.info("LKLSM交易状态同步处理: trade_no={}, merchant_no={}, out_trade_no={}, trade_status={}, total_amount={}",
                transNotify.getTrade_no(), transNotify.getMerchant_no(), transNotify.getOut_trade_no(),
                transNotify.getTrade_status(), transNotify.getTotal_amount());

        // 1. 根据out_trade_no查询是否已存在记录
        TransExtendFlowEntity transExtendFlow = transExtendFlowSyncMapper.findByChannelFlowId(transNotify.getOut_trade_no());
        if (transExtendFlow == null) {
            throw new OptimisticException("PC_TRANS_LKLSM_TOPIC，数据同步异常，交易记录不存在: " + transNotify.getOut_trade_no());
        }

        // 2. 更新交易状态和相关信息
        updateTransStatus(transNotify, transExtendFlow);
    }

    /**
     * 根据交易通知更新交易状态和相关信息
     *
     * @param transNotify 交易通知DTO，包含交易状态、金额等信息
     * @param transFlow 交易流水实体
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateTransStatus(LklSmTransNotifyDto transNotify, TransExtendFlowEntity transFlow) {
        // 1. 根据流水号查询交易记录并加锁
        TransExtendFlowEntity updateEntity = transExtendFlowSyncMapper.findbyFlowIdForUpdate(transFlow.getFlowId());
        if (updateEntity == null) {
            throw new OptimisticException("交易记录不存在: " + transFlow.getFlowId());
        }

        processTransNotifyDetails(transNotify, updateEntity);

        // 2. 更新交易状态和相关信息
        updateEntity.setTransStatus(mapTradeStatus(transNotify.getTrade_status()));
        updateEntity.setUpdateTime(new java.util.Date());

        // 3. 更新交易完成时间
        if (transNotify.getTrade_time() != null && !transNotify.getTrade_time().isEmpty()) {
            try {
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyyMMddHHmmss");
                updateEntity.setCompleteTime(sdf.parse(transNotify.getTrade_time()));
            } catch (Exception e) {
                log.warn("解析交易完成时间失败: {}", transNotify.getTrade_time(), e);
            }
        }

        // 4. 更新银行卡类型
        if (transNotify.getCard_type() != null) {
            updateEntity.setCardType(transNotify.getCard_type());
        }

        // 5. 更新发卡行信息
        if (transNotify.getBank_type() != null) {
            updateEntity.setBankCode(transNotify.getBank_type());
        }

        int updateCount = transExtendFlowSyncMapper.updateLklSmSyncByFlowId(updateEntity);
        if (updateCount != 1) {
            throw new OptimisticException("交易状态更新失败，影响行数不为1: trade_no=" + transNotify.getTrade_no());
        }
    }



    /**
     * 处理交易通知的详细信息
     *
     * @param transNotify 交易通知DTO
     * @param transFlow 交易流水实体
     */
    public void processTransNotifyDetails(LklSmTransNotifyDto transNotify, TransExtendFlowEntity transFlow) {
        try {
            // 1. 处理基础交易信息
            processBasicTransInfo(transNotify, transFlow);

            // 2. 处理金额相关信息
            processAmountInfo(transNotify, transFlow);

            // 3. 处理花呗分期信息
            processHbFqPayInfo(transNotify, transFlow);

            // 4. 处理合单信息
            processOutSplitInfo(transNotify, transFlow);

            // 5. 处理单品券优惠信息
            processDiscountGoodsDetail(transNotify, transFlow);

            // 6. 处理用户标识信息
            processUserInfo(transNotify, transFlow);

        } catch (Exception e) {
            log.error("处理交易通知详细信息失败: trade_no={}", transNotify.getTrade_no(), e);
            throw new OptimisticException("处理交易通知详细信息失败", e);
        }
    }

    /**
     * 处理基础交易信息
     */
    private void processBasicTransInfo(LklSmTransNotifyDto transNotify, TransExtendFlowEntity transFlow) {
        // 设置拉卡拉交易流水号
        if (StringUtils.hasText(transNotify.getTrade_no())) {
            transFlow.setChannelFlowId(transNotify.getTrade_no());
        }

        // 设置账户类型
        if (StringUtils.hasText(transNotify.getAccount_type())) {
            // 根据账户类型设置交易途径
            String transWay = mapAccountTypeToTransWay(transNotify.getAccount_type());
            transFlow.setTransWay(transWay);
        }

        // 设置结算商户号和终端号
        if (StringUtils.hasText(transNotify.getSettle_merchant_no())) {
            transFlow.setChannelCustomerNo(transNotify.getSettle_merchant_no());
        }

        if (StringUtils.hasText(transNotify.getSettle_term_no())) {
            transFlow.setPosCati(transNotify.getSettle_term_no());
        }

        // 设置备注信息
        if (StringUtils.hasText(transNotify.getRemark())) {
            transFlow.setRemitMsg(transNotify.getRemark());
        }
    }

    /**
     * 处理金额相关信息
     */
    private void processAmountInfo(LklSmTransNotifyDto transNotify, TransExtendFlowEntity transFlow) {
        // 订单金额
        if (StringUtils.hasText(transNotify.getTotal_amount())) {
            try {
                BigDecimal totalAmount = new BigDecimal(transNotify.getTotal_amount()).divide(new BigDecimal("100"));
                transFlow.setAmount(totalAmount);
            } catch (NumberFormatException e) {
                log.warn("解析订单金额失败: {}", transNotify.getTotal_amount(), e);
            }
        }

        // 付款人实付金额
        if (StringUtils.hasText(transNotify.getPayer_amount())) {
            try {
                BigDecimal payerAmount = new BigDecimal(transNotify.getPayer_amount()).divide(new BigDecimal("100"));
                // 可以存储在自定义字段中，或者根据业务需要处理
                log.info("付款人实付金额: {}", payerAmount);
            } catch (NumberFormatException e) {
                log.warn("解析付款人实付金额失败: {}", transNotify.getPayer_amount(), e);
            }
        }

        // 账户端结算金额
        if (StringUtils.hasText(transNotify.getAcc_settle_amount())) {
            try {
                BigDecimal accSettleAmount = new BigDecimal(transNotify.getAcc_settle_amount()).divide(new BigDecimal("100"));
                transFlow.setSettleAmount(accSettleAmount);
            } catch (NumberFormatException e) {
                log.warn("解析账户端结算金额失败: {}", transNotify.getAcc_settle_amount(), e);
            }
        }

        // 处理各种优惠金额
        processDiscountAmounts(transNotify, transFlow);
    }

    /**
     * 处理优惠金额信息
     */
    private void processDiscountAmounts(LklSmTransNotifyDto transNotify, TransExtendFlowEntity transFlow) {
        StringBuilder discountInfo = new StringBuilder();

        // 商户侧优惠金额
        if (StringUtils.hasText(transNotify.getAcc_mdiscount_amount())) {
            discountInfo.append("商户优惠:").append(transNotify.getAcc_mdiscount_amount()).append("分;");
        }

        // 账户端优惠金额
        if (StringUtils.hasText(transNotify.getAcc_discount_amount())) {
            discountInfo.append("账户端优惠:").append(transNotify.getAcc_discount_amount()).append("分;");
        }

        // 账户端其它优惠金额
        if (StringUtils.hasText(transNotify.getAcc_other_discount_amount())) {
            discountInfo.append("其它优惠:").append(transNotify.getAcc_other_discount_amount()).append("分;");
        }

        if (discountInfo.length() > 0) {
            // 将优惠信息存储到备注字段或自定义字段中
            log.info("优惠信息: {}", discountInfo.toString());
        }
    }

    /**
     * 处理花呗分期支付信息
     */
    private void processHbFqPayInfo(LklSmTransNotifyDto transNotify, TransExtendFlowEntity transFlow) {
        if (transNotify.getHb_fq_pay_info() != null) {
            LklSmTransNotifyDto.HbFqPayInfo hbFqInfo = transNotify.getHb_fq_pay_info();
            StringBuilder fqInfo = new StringBuilder();

            if (StringUtils.hasText(hbFqInfo.getFq_amount())) {
                fqInfo.append("分期金额:").append(hbFqInfo.getFq_amount()).append("分;");
            }

            if (StringUtils.hasText(hbFqInfo.getUser_install_num())) {
                fqInfo.append("分期期数:").append(hbFqInfo.getUser_install_num()).append("期;");
            }

            if (fqInfo.length() > 0) {
                log.info("花呗分期信息: {}", fqInfo.toString());
                // 可以将分期信息存储到自定义字段中
            }
        }
    }

    /**
     * 处理合单信息
     */
    private void processOutSplitInfo(LklSmTransNotifyDto transNotify, TransExtendFlowEntity transFlow) {
        if (transNotify.getOut_split_rsp_infos() != null && !transNotify.getOut_split_rsp_infos().isEmpty()) {
            try {
                String splitInfoJson = JSON.toJSONString(transNotify.getOut_split_rsp_infos());
                log.info("合单信息: {}", splitInfoJson);
                // 可以将合单信息存储到自定义字段中
            } catch (Exception e) {
                log.warn("处理合单信息失败", e);
            }
        }
    }

    /**
     * 处理单品券优惠信息
     */
    private void processDiscountGoodsDetail(LklSmTransNotifyDto transNotify, TransExtendFlowEntity transFlow) {
        if (StringUtils.hasText(transNotify.getDiscount_goods_detail())) {
            try {
                log.info("单品券优惠信息: {}", transNotify.getDiscount_goods_detail());
                // 可以解析JSON格式的优惠商品信息并存储
            } catch (Exception e) {
                log.warn("处理单品券优惠信息失败: {}", transNotify.getDiscount_goods_detail(), e);
            }
        }
    }

    /**
     * 处理用户标识信息
     */
    private void processUserInfo(LklSmTransNotifyDto transNotify, TransExtendFlowEntity transFlow) {
        StringBuilder userInfo = new StringBuilder();

        if (StringUtils.hasText(transNotify.getUser_id1())) {
            userInfo.append("用户标识1:").append(transNotify.getUser_id1()).append(";");
        }

        if (StringUtils.hasText(transNotify.getUser_id2())) {
            userInfo.append("用户标识2:").append(transNotify.getUser_id2()).append(";");
            // 对于微信openId，可以存储到专门的字段
            if ("WECHAT".equals(transNotify.getAccount_type())) {
                transFlow.setOpenId(transNotify.getUser_id2());
            }
        }

        if (StringUtils.hasText(transNotify.getAcc_activity_id())) {
            userInfo.append("活动ID:").append(transNotify.getAcc_activity_id()).append(";");
            // transFlow.setPolicyId(transNotify.getAcc_activity_id());
        }

        if (userInfo.length() > 0) {
            log.info("用户信息: {}", userInfo.toString());
        }
    }

    /**
     * 将账户类型映射为交易途径
     */
    private String mapAccountTypeToTransWay(String accountType) {
        if (accountType == null) {
            return null;
        }
        switch (accountType) {
            case "WECHAT":
                return "WECHAT_PAY";
            case "ALIPAY":
                return "ALIPAY";
            case "UQRCODEPAY":
                return "UNION_PAY";
            case "BESTPAY":
                return "BESTPAY";
            case "SUNING":
                return "SUNING_PAY";
            case "DCPAY":
                return "DCEP";
            default:
                return accountType;
        }
    }

    /**
     * 解析时间字符串
     */
    private Date parseDateTime(String dateTimeStr) {
        if (!StringUtils.hasText(dateTimeStr)) {
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            return sdf.parse(dateTimeStr);
        } catch (Exception e) {
            log.warn("解析时间失败: {}", dateTimeStr, e);
            return null;
        }
    }

    /**
     * 映射拉卡拉交易状态到系统内部状态
     */
    private String mapTradeStatus(String lklStatus) {
        if (lklStatus == null) {
            return null;
        }
        switch (lklStatus) {
            case "INIT":
                return "INIT";
            case "CREATE":
                return "CREATE";
            case "SUCCESS":
                return "SUCCESS";
            case "FAIL":
                return "FAIL";
            case "DEAL":
                return "PROCESSING";
            case "UNKNOWN":
                return "UNKNOWN";
            case "CLOSE":
                return "CLOSE";
            case "PART_REFUND":
                return "PART_REFUND";
            case "REFUND":
                return "REFUND";
            case "REVOKED":
                return "REVOKED";
            default:
                return lklStatus;
        }
    }
}
