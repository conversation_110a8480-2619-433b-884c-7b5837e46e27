# LKLSM交易通知系统

## 概述

LKLSM交易通知系统负责处理来自拉卡拉的交易通知消息，包括交易状态更新、金额信息处理、花呗分期信息、合单信息、单品券优惠信息等复杂业务逻辑。

## 系统架构

```
RocketMQ消息 -> LklSmTransSyncConsumer -> LklSmTransDataSyncBiz -> LklSmTransSyncService -> 数据库更新
                                                                      |
                                                                      v
                                                            LklSmTransNotifyProcessService
                                                            (处理复杂业务逻辑)
```

## 核心组件

### 1. LklSmTransNotifyDto
交易通知数据传输对象，包含所有拉卡拉交易通知字段：

#### 基础字段
- `merchant_no`: 商户号 (必填, String(32))
- `out_trade_no`: 商户交易流水号 (必填, String(64))
- `trade_no`: 拉卡拉交易流水号 (必填, String(32))
- `log_no`: 拉卡拉对账单流水号 (必填, String(14))
- `acc_trade_no`: 账户端交易订单号 (必填, String(32))
- `account_type`: 钱包类型 (必填, String(32))
  - WECHAT: 微信
  - ALIPAY: 支付宝
  - UQRCODEPAY: 银联
  - BESTPAY: 翼支付
  - SUNING: 苏宁易付宝
  - DCPAY: 数字人民币
- `trade_status`: 交易状态 (必填, String(16))
  - INIT: 初始化
  - CREATE: 下单成功
  - SUCCESS: 交易成功
  - FAIL: 交易失败
  - DEAL: 交易处理中
  - UNKNOWN: 未知状态
  - CLOSE: 订单关闭
  - PART_REFUND: 部分退款
  - REFUND: 全部退款
  - REVOKED: 订单撤销

#### 金额字段
- `total_amount`: 订单金额 (必填, String(12), 单位分)
- `payer_amount`: 付款人实付金额 (可选, String(12), 单位分)
- `acc_settle_amount`: 账户端结算金额 (可选, String(12), 单位分)
- `acc_mdiscount_amount`: 商户侧优惠金额 (可选, String(12), 单位分)
- `acc_discount_amount`: 账户端优惠金额 (可选, String(12), 单位分)
- `acc_other_discount_amount`: 账户端其它优惠金额 (可选, String(12), 单位分)

#### 嵌套对象

##### HbFqPayInfo (花呗分期支付信息)
```java
public static class HbFqPayInfo {
    private String fq_amount;        // 分期金额，单位分
    private String user_install_num; // 分期期数
}
```

##### OutSplitRspInfo (合单信息)
```java
public static class OutSplitRspInfo {
    private String sub_trade_no;     // 子单拉卡拉流水号
    private String sub_log_no;       // 子单对账流水号
    private String out_sub_trade_no; // 子单外部流水号
    private String merchant_no;      // 子单商户号
    private String term_no;          // 子单终端号
    private String amount;           // 子单金额，单位分
}
```

##### DiscountGoodsDetail (单品券优惠信息)
```java
public static class DiscountGoodsDetail {
    private String goods_id;         // 商品id
    private String goods_name;       // 商品名称
    private String discount_amount;  // 优惠金额
    private String voucher_id;       // 优惠id
}
```

### 2. LklSmTransSyncConsumer
RocketMQ消息消费者，负责接收交易通知消息。

**配置信息:**
- Topic: `PC_TRANS_LKLSM_TOPIC`
- ConsumerGroup: `PC_TRANS_LKLSM_TOPIC_SYNC_GROUP`
- 消费线程数: 20

### 3. LklSmTransDataSyncBiz
业务处理类，负责分布式锁控制和异常处理。

**功能:**
- 使用Redis分布式锁防止重复消费
- 异常处理和钉钉告警
- 事务控制

### 4. LklSmTransSyncService
核心同步服务，负责交易状态更新。

**主要方法:**
- `syncTrans()`: 同步交易信息
- `updateTransStatus()`: 更新交易状态
- `mapTradeStatus()`: 交易状态映射

### 5. LklSmTransNotifyProcessService
复杂业务逻辑处理服务。

**处理内容:**
- 基础交易信息处理
- 金额信息处理
- 花呗分期信息处理
- 合单信息处理
- 单品券优惠信息处理
- 用户标识信息处理

## 数据库映射

### TransExtendFlowEntity 字段映射

| 通知字段 | 实体字段 | 说明 |
|---------|---------|------|
| trade_no | channelFlowId | 拉卡拉交易流水号 |
| trade_status | transStatus | 交易状态 |
| account_type | transWay | 交易途径 |
| total_amount | amount | 交易金额(转换为元) |
| acc_settle_amount | settleAmount | 结算金额(转换为元) |
| trade_time | completeTime | 交易完成时间 |
| card_type | cardType | 银行卡类型 |
| bank_type | bankCode | 发卡行代码 |
| user_id2 | openId | 微信openId |
| acc_activity_id | policyId | 活动ID |

## 使用示例

### 1. 创建交易通知DTO
```java
LklSmTransNotifyDto transNotify = new LklSmTransNotifyDto();
transNotify.setMerchant_no("TEST_MERCHANT_001");
transNotify.setOut_trade_no("OUT_TRADE_20250731001");
transNotify.setTrade_no("LKL_TRADE_20250731001");
transNotify.setTrade_status("SUCCESS");
transNotify.setTotal_amount("10000"); // 100.00元

// 设置花呗分期信息
LklSmTransNotifyDto.HbFqPayInfo hbFqInfo = new LklSmTransNotifyDto.HbFqPayInfo();
hbFqInfo.setFq_amount("10000");
hbFqInfo.setUser_install_num("3");
transNotify.setHb_fq_pay_info(hbFqInfo);
```

### 2. 发送消息到RocketMQ
```java
// 消息会被LklSmTransSyncConsumer自动消费处理
rocketMQTemplate.convertAndSend("PC_TRANS_LKLSM_TOPIC", transNotify);
```

## 配置说明

### application.yml 配置
```yaml
rocketmq:
  name-server: localhost:9876
  producer:
    group: lklsm-trans-producer
  consumer:
    group: lklsm-trans-consumer
```

### Redis 配置
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
```

## 监控和告警

### 钉钉告警
系统会在以下情况发送钉钉告警：
- 交易记录不存在
- 数据处理异常
- 数据库更新失败

### 日志监控
关键日志记录：
- 消息接收日志
- 业务处理日志
- 异常错误日志
- 性能监控日志

## 测试

### 单元测试
运行测试类：
```bash
mvn test -Dtest=LklSmTransSyncServiceTest
```

### 集成测试
1. 启动RocketMQ服务
2. 启动Redis服务
3. 启动应用
4. 发送测试消息

## 注意事项

1. **幂等性**: 系统使用Redis分布式锁确保消息不重复处理
2. **事务性**: 所有数据库操作都在事务中执行
3. **异常处理**: 所有异常都会记录日志并发送告警
4. **性能**: 消费者支持多线程并发处理
5. **数据一致性**: 使用乐观锁确保数据更新的一致性

## 扩展说明

如需扩展新的业务逻辑：
1. 在`LklSmTransNotifyProcessService`中添加新的处理方法
2. 在`LklSmTransSyncService.syncTrans()`中调用新方法
3. 更新数据库映射配置
4. 添加相应的单元测试

## 故障排查

### 常见问题
1. **消息消费失败**: 检查RocketMQ连接和配置
2. **数据库更新失败**: 检查数据库连接和事务配置
3. **Redis锁获取失败**: 检查Redis连接和锁超时配置
4. **JSON解析失败**: 检查消息格式和DTO定义

### 日志查看
```bash
# 查看消费者日志
tail -f logs/lklsm-trans-sync.log

# 查看错误日志
grep "ERROR" logs/lklsm-trans-sync.log
```
