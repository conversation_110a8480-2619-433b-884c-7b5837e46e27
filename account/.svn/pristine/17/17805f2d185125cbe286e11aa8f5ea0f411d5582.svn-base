package com.pay.account.channel.zhongfu.assembler;


import com.pay.account.channel.bean.req.FoRepushReq;
import com.pay.account.channel.bean.req.RemitQueryReq;
import com.pay.account.channel.bean.res.FoRepushRes;
import com.pay.account.channel.bean.res.RemitQueryRes;
import com.pay.account.channel.bean.res.RemitRes;
import com.pay.account.core.remote.bean.settle.req.SettleFoRepushInstructReqDto;
import com.pay.account.core.remote.bean.settle.req.SettleInstructFoQueryReqDto;
import com.pay.account.core.remote.bean.settle.res.SettleFoRepushInstructRspDto;
import com.pay.account.core.remote.bean.settle.res.SettleInstructFoQueryRspDto;
import com.pay.account.core.remote.bean.settle.res.SettleInstructRspDto;
import com.pay.frame.common.base.enums.Origin;
import com.pay.frame.common.base.enums.RemitStatus;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ZFTransAssembler {

    public RemitRes toRemitRes(SettleInstructRspDto object) {
        RemitRes remitRes = RemitRes.builder().build();
        return remitRes;
    }

    public SettleInstructFoQueryReqDto toRemitQuery(RemitQueryReq remitQueryReq) {
        SettleInstructFoQueryReqDto zfSettleFoQueryReqDto = SettleInstructFoQueryReqDto.builder()
                .origin(Origin.ZF.name())
                .thirdCustNo(remitQueryReq.getChannelCustNo())
                .flowId(remitQueryReq.getChannelTransOrder())
                .build();
        return zfSettleFoQueryReqDto;
    }

    public RemitQueryRes toRemitQueryRes(SettleInstructFoQueryRspDto zfSettleFoQueryRspDto) {
        RemitQueryRes remitQueryRes = RemitQueryRes.builder()
                .channelResMsg(zfSettleFoQueryRspDto.getMessage())
                .remitStatus(assemblerRemitSatus(zfSettleFoQueryRspDto))
                .remitAmount(zfSettleFoQueryRspDto.getFoAmt())
                .channelResCode(zfSettleFoQueryRspDto.getCode())
                .bankAccountNo(zfSettleFoQueryRspDto.getSettleCardShortNo())
                .channelSettleCycle(zfSettleFoQueryRspDto.getSettleCycle())
                .channelFoSerialId(zfSettleFoQueryRspDto.getFoSerialId())
                .channelDigAmt(zfSettleFoQueryRspDto.getDigAmt())
                .channelDigStatus(zfSettleFoQueryRspDto.getDigStatus())
                .build();
        return remitQueryRes;
    }

    private String assemblerRemitSatus(SettleInstructFoQueryRspDto zfSettleFoQueryRspDto) {
        if (RemitStatus.FAIL.name().equals(zfSettleFoQueryRspDto.getFoStatus())) {
            return RemitStatus.FAIL.name();
        }

        if (RemitStatus.SUCCESS.name().equals(zfSettleFoQueryRspDto.getFoStatus())) {
            return RemitStatus.SUCCESS.name();
        }

        return RemitStatus.CONFIRM.name();
    }

    public SettleFoRepushInstructReqDto toFoRepush(FoRepushReq remitReq) {
        SettleFoRepushInstructReqDto settleFoRepushInstructReqDto = SettleFoRepushInstructReqDto.builder()
                .origin(Origin.ZF.name())
                .thirdCustNo(remitReq.getChannelCustNo())
                .flowId(remitReq.getTransOrder())
                .build();
        return settleFoRepushInstructReqDto;

    }

    public FoRepushRes toFoRepushRes(SettleFoRepushInstructRspDto object) {
        return FoRepushRes.builder().build();
    }
}
