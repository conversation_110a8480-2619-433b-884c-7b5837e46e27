package com.pay.account.core.remote.bean.proxyPay.req;

import java.math.BigDecimal;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotEmpty;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> @date 2024/7/25
 * @apiNote 描述：接入方调用中付，从接入方的ACS账户出款到指定的银行账户。
 * 备注：
 * 1、收款方银行账户实际到账金额 = 代付出款金额
 * 2、代付的手续费由付款方承担
 * 3、付款方的账户扣减金额 = 代付出款金额+代付手续费
 */
@Data
@ToString(callSuper = true)
@SuperBuilder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FundoutProxyReqDto {
	private String origin;
    /**
     * ZF、LS、YS LKL LD
     * 外部订单号 接入方提供，同userCode下唯一
     */
    @NotEmpty(message = "订单号不能为空")
    private String outTradeNo;

    /**
     * ZF
     * 代付出款类型
     * 1：对私出款
     * 2：对公出款
     */
    private String bankType;

    /**
     * ZF、LS、YS
     * 收款方手机号
     */
    private String phoneNo;

    /**
     * ZF、LS、YS
     * 收款方姓名
     */
    private String name;

    /**
     * ZF、LS、YS
     * 收款方身份证号码 如有x，传大写X；
     * 对私必传
     */
    private String idCardNo;

    /**
     * ZF、LS、YS
     * 收款方账号  卡号
     */
    @NotEmpty(message = "收款方账号不能为空")
    private String bankAcctId;

    /**
     * ZF
     * 出款时间 格式：yyyy-MM-dd HH:mm:ss.SSS
     */
    private String payTime;

    /**
     * ZF、LS、YS
     * 代付出款金额 单位（元）
     */
    @NotNull(message = "代付出款不能为空")
    private BigDecimal amount;

    /**
     * ZF、LS、YS LKL LD
     * 付款账户类型：
     * 01：营销管理账户
     * 02：商户付款账号
     */
    @NotEmpty(message = "付款账户类型不能为空")
    private String accountType;

    /**
     * ZF
     * 代付场景
     * REPAY：支付货款
     * COM_ROF：合作伙伴佣金发放
     * MR：合作伙伴营销奖励（小微流量卡费挖款）
     * ACT_ROF： 合作伙伴激活款发放（小微押金挖款）
     * BD： 合作伙伴业务往来
     * SALARY_ROF： 工资发放
     */
    private String payScene;

    /**
     * ZF
     * 代付出款 代付出款备注，不超过32个汉字
     */
    private String remark;

    /**
     * LS、YS
     * 	银行总行号，请参考乐刷附录
     */
    private String headBankCode;

    /**
     * ZF、LS、YS
     * 收款方 总行银行名称
     */
    private String headBankName;

    /**
     * ZF、YS LS
     * 开户支行
     * 开户支行名称
     * 对公必传
     */
    private String alliedBankName;

    /**
     * ZF、LS、YS
     * 联行号 开户行联行号
     * 对公必传
     */
    private String alliedBankCode;

    /**
     * ZF
     * 开户银行所在省份编码  省份编码（4位银联码）
     * 对公必传
     */
    private String provinceCode;

    /**
     * ZF
     * 开户银行所在城市编码 城市编码（4位银联码）
     * 对公必传
     */
    private String cityCode;

    /**
     * LS、YS
     * 银行开户行所在城市，eg：深圳市
     */
    private String cityName;

    /**
     * ZF
     * 开户行区县代码 区县编码（4位银联码）
     */
    private String districtCode;

    /**
     * LS、YS
     * 身份证正面URL -- 人像面
     */
    private String idCardFrontURL;

    /**
     * LS、YS
     * 身份证背面URL -- 国徽面
     */
    private String idCardBackURL;


}
