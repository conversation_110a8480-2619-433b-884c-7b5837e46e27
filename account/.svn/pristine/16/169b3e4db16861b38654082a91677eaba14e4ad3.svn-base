package com.pay.account.core.dto.req.bc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

/**
 *
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BcWithDrawDetailReq {

    /** 编号 */
    @NotBlank(message = "用户编号不能为空")
    private String userNo;

    @NotBlank(message = "付款流水号不能为空")
    private String transOrder;


}
