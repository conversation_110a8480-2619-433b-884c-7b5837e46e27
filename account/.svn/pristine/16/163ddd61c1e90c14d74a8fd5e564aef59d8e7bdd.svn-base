<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.account.core.mapper.TransExtendFlowMapper">
    <resultMap id="BaseResultMap" type="com.pay.account.core.dto.rsp.SystemIncomeSummary">
        <result column="TRANS_DATE" jdbcType="VARCHAR" property="transDate"/>
        <result column="MARKETING_INCOME" jdbcType="DECIMAL" property="marketingIncome"/>
        <result column="SIM_INCOME" jdbcType="DECIMAL" property="simIncome"/>
        <result column="PROFIT_INCOME" jdbcType="DECIMAL" property="profitIncome"/>
        <result column="ORIGIN" jdbcType="VARCHAR" property="origin"/>
    </resultMap>


    <select id="systemIncomeSummary" resultMap="BaseResultMap">
        select
        TO_CHAR(TRANS_TIME, 'YYYY-MM-DD') TRANS_DATE,  nvl(SUM(MARKETING_EARN), 0) + nvl(SUM(SIM_CARD_FEE), 0) MARKETING_INCOME,
        NVL(SUM(TRANS_EARN), 0) PROFIT_INCOME,ORIGIN
        from ZF_OPER_ADM.TRANS_EXTEND_FLOW
        where TRANS_TIME &gt;= TO_DATE(#{transDate} || ' 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
        and TRANS_TIME &lt;= TO_DATE(#{transDate} || ' 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
        group by TO_CHAR(TRANS_TIME, 'YYYY-MM-DD'),ORIGIN
    </select>

</mapper>