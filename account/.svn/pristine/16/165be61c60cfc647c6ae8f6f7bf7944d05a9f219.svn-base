package com.pay.account.core.config;

import javax.annotation.Resource;

import com.pay.account.channel.leshua.client.LSClient;
import com.pay.account.channel.leshua.client.LSTransClient;
import com.pay.account.channel.yinsheng.client.YSClient;
import com.pay.account.channel.yinsheng.client.YSTransClient;
import com.pay.account.channel.zhongfu.client.ZFCorpClient;
import org.springframework.context.annotation.Configuration;

import com.pay.account.channel.ChannelInterface;
import com.pay.account.channel.qxb.client.QxbClient;
import com.pay.account.channel.zhongfu.client.ZFClient;
import com.pay.account.channel.zhongfu.client.ZFTransClient;
import com.pay.account.core.enums.HandlerResult;
import com.pay.account.core.exception.BizRuntimeException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024-01-13 11:27:35
 **/
@Slf4j
@Configuration
public class RemitChannelFactory {

    @Resource
    private QxbClient qxbClient;

    @Resource
    private ZFClient zfMktClient;

    @Resource
    private ZFCorpClient zFCorpClient;

    @Resource
    private ZFTransClient zfTransClient;

    @Resource
    private YSClient ysMktClient;

    @Resource
    private YSTransClient ysTransClient;

    @Resource
    private LSClient lsMktClient;

    @Resource
    private LSTransClient lsTransClient;

    public ChannelInterface getRemitChannelClient(String channelNo) {

        switch (channelNo) {
            case "qixinbao":
                return qxbClient;
            case "zf_marketing":
                return zfMktClient;
            case "zf_trans":
                return zfTransClient;
            case "ys_marketing":
                return ysMktClient;
            case "ys_trans":
                return ysTransClient;
            case "ls_marketing":
                return lsMktClient;
            case "ls_trans":
                return lsTransClient;
            case "zf_corp_marketing":
                return zFCorpClient;
            default:
                throw new BizRuntimeException(HandlerResult.UNKNOWN_REMIT_CHANNEL);
        }
    }
}
