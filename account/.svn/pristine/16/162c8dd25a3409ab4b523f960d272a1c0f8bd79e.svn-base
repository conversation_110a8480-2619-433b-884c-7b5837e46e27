package com.pay.account.core.enums;

import com.pay.frame.common.base.enums.Origin;

public enum OriginSysUserNo {

	ZF_ORIGIN("S000000001", Origin.ZF.name()),

	LS_ORIGIN("S000000002", Origin.LS.name()),

	YS_ORIGIN("S000000003", Origin.YS.name()),

	PYP_ORIGIN("S000000004", Origin.PYP.name()),
	;

	private String userNo;

	private String origin;

	public String getUserNo() {
		return userNo;
	}

	public String getOrigin() {
		return origin;
	}
    
	OriginSysUserNo(String userNo, String origin){
		this.userNo = userNo;
        this.origin = origin;
	}
	
}
