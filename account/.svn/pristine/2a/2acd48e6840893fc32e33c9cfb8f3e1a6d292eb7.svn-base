package com.pay.account.core.dto.req.account;

import java.util.List;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import lombok.Data;

/**
 * 账户资金查询请求对象
 *
 * <AUTHOR>
 * @date 2021-11-18 19:19:37
 **/
@Data
public class AccountFundQueryReq {


    @NotNull(message = "开始日期不能为空")
    @Pattern(message = "时间格式不正确 应为：yyyy-MM-dd", regexp = "^\\d{4}-(0[0-9]|1[0,1,2])-([0,1][0-9]|[2][0-9]|[3][0,1])$")
    private String createTimeStart;

    /**
     * 开始时间
     */
    @NotNull(message = "结束日期不能为空")
    @Pattern(message = "时间格式不正确 应为：yyyy-MM-dd", regexp = "^\\d{4}-(0[0-9]|1[0,1,2])-([0,1][0-9]|[2][0-9]|[3][0,1])$")
    private String createTimeEnd;

    /**
     * 用户编号
     */
    @NotNull(message = "用户编号不能为空")
    private String userNo;

    /**
     * 资金方向
     */
    @NotNull(message = "资金方向")
    private String fundSymbol;

    /**
     * 说明描述
     */
    private String remark;

    @NotNull(message = "账户类型不能为空")
    private List<String> accTypes;

    @NotNull(message = "记账科目不能为空")
    private List<String> bizTypes;

    /**
     * 当前页
     */
    private Integer currentPage;

    /**
     * 条数
     */
    private Integer pageSize = 10;
}
