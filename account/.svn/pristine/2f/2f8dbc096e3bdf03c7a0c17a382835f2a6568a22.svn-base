package com.pay.account.core.dto.req.account;

import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;

import com.pay.frame.common.base.enums.AccountType;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version [版本号, 2018年12月8日 下午1:37:10]
 * @Description 记账请求
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Data
@ToString(callSuper = true)
public class AccountKeepReq extends BaseRequest {

    private static final long serialVersionUID = -7589055628609078672L;

    /**
     * 用户编号
     */
    @NotBlank(message = "用户编号不能为空")
    private String userNo;
    
    @NotBlank(message = "用户角色不能为空")
    private String userRole;


    /**
     * 账户类型
     */
    @NotNull(message = "账户类型不能为空")
    private AccountType accountType;

    /**
     * 资金变更流水
     */
    private List<AccountTradeBean> tradeBeans = new ArrayList<>();
}
