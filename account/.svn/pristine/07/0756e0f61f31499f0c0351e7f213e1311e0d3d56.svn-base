package com.pay.account.core.dto.rsp.remit;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 *
 */
@Data
@ToString(callSuper = true)
public class WithdrawConfigBo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 最小额
     */
    private BigDecimal minLimit;

    /**
     * 最大额
     */
    private BigDecimal maxLimit;

    /**
     * 提现次数
     */
    private Integer numLimit;
    
    /**
     * 费率
     */
    private BigDecimal rate;

    /**
     * 费率类型
     */
    private String feeType;

    /**
     * 其他计费（固定）
     */
    private BigDecimal extAmt;

    /**
     * 开始时间
     */
    private String timeStart;
    /**
     * 结束时间
     */
    private String timeEnd;

    /**
     * 提现业务类型
     */
    private String businessCode;
    
    /**
     * 反成本
     */
    private BigDecimal cashbackCost;

    /** 
     * 通道成本
     * */
    private BigDecimal channelCost;
}
