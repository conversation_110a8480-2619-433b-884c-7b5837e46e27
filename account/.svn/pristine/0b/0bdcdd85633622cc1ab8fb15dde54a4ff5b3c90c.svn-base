package com.pay.account.core.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import com.pay.account.core.entity.BaseEntity;

import com.pay.frame.common.base.enums.Origin;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 提现记录
 */
@Data
@ToString(callSuper = true)
public class WithdrawRecord extends BaseEntity implements Serializable {

    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 用户号
     */
    private String userNo;

    /**
     * 用户角色
     */
    private String userRole;

    /**
     * 请求流水号
     */
    private String transOrder;

    /**
     * 提现请求时间
     */
    private Date transTime;

    /**
     * 业务类型
     */
    private String businessCode;

    /**
     * 提现金额
     */
    private BigDecimal amount;

    /**
     * 付款金额
     */
    private BigDecimal remitAmount;

    /**
     * 提现费率
     */
    private BigDecimal rate;

    /**
     * 比例费用
     */
    private BigDecimal rateFee;

    /**
     * 固定费用
     */
    private BigDecimal fixFee;

    /**
     * 全部费用
     */
    private BigDecimal totalFee;

    /**
     * 卡类型
     */
    private String cardType;

    /**
     * 总行名称
     */
    private String bankName;

    /**
     * 总行行号
     */
    private String bankCode;

    /**
     * 银行账户编号
     */
    private String bankAccountNo;

    /**
     * 银行账户名称
     */
    private String bankAccountName;

    /**
     * 身份证号
     */
    private String identityNo;

    /**
     * 预留手机号
     */
    private String phoneNo;

    /**
     * 状态
     */
    private String status;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 系统来源
     */
    private String sysSource;

    /**
     * 备注
     */
    private String remark;

    /**
     * 反成本
     */
    private BigDecimal cashbackCost;

    /**
     * 通道成本
     */
    private BigDecimal channelCost;

    /**
     * 机构来源
     */
    private Origin origin;

    /**
     * "省份编码不能为空")
     */
    private String provinceCode;
    /**
     * "城市编码不能为空")\
     */
    private String cityCode;
    /**
     * "区县编码不能为空")
     */
    private String districtCode;
    /**
     * "开户支行名称不能为空")
     */
    private String branchBankName;
    /**
     * "开户支行名称不能为空"
     */
    private String alliedBankCode;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof WithdrawRecord)) {
            return false;
        }
        WithdrawRecord that = (WithdrawRecord) o;
        return Objects.equals(userNo, that.userNo) &&
                Objects.equals(userRole, that.userRole) &&
                Objects.equals(transOrder, that.transOrder) &&
                Objects.equals(businessCode, that.businessCode) &&
                amount.compareTo(that.amount) == 0 &&
                remitAmount.compareTo(that.remitAmount) == 0 &&
                rate.compareTo(that.rate) == 0 &&
                rateFee.compareTo(that.rateFee) == 0 &&
                fixFee.compareTo(that.fixFee) == 0 &&
                totalFee.compareTo(that.totalFee) == 0 &&
                Objects.equals(sysSource, that.sysSource);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userNo, userRole, transOrder, amount, businessCode, sysSource);
    }
}
