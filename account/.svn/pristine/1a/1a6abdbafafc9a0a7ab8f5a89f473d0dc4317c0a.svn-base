package com.pay.account.core.biz.account;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageInfo;
import com.pay.account.core.constants.SysConstants;
import com.pay.account.core.dto.req.account.AccountKeepReq;
import com.pay.account.core.dto.req.account.AccountTradeBean;
import com.pay.account.core.dto.rsp.account.AccountKeepRes;
import com.pay.account.core.entity.Account;
import com.pay.account.core.entity.AccountAdjustBill;
import com.pay.account.core.enums.AdjustStatus;
import com.pay.account.core.enums.AdjustType;
import com.pay.account.core.enums.HandlerResult;
import com.pay.account.core.exception.BizRuntimeException;
import com.pay.account.core.service.account.AccountAdjustService;
import com.pay.account.core.service.account.AccountService;
import com.pay.account.core.utils.DateUtil;
import com.pay.account.core.utils.JsonUtils;
import com.pay.frame.common.base.enums.AccountType;
import com.pay.frame.common.base.enums.BizType;
import com.pay.frame.common.base.enums.account.AccountStatus;
import com.pay.frame.common.base.enums.account.AuditStatus;
import com.pay.frame.common.base.exception.ServerException;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024-02-26 18:34:44
 **/
@Slf4j
@Component
public class AccountAdjustBiz {

    @Autowired
    private AccountAdjustService accountAdjustService;
    @Autowired
    private AccountKeepBiz accountKeepBiz;
    @Autowired
    private AccountService accountService;


    @Transactional(rollbackFor = Exception.class)
    public void addAdjustBill(AccountAdjustBill adjustBill) {
        log.info("新增调账开始:{}", adjustBill);
        Account account = accountService.findByAccNo(adjustBill.getAccNo());

        if (null == account) {
            log.info("账户信息查询：账户不存在");
            throw new BizRuntimeException(HandlerResult.ACCOUNT_NOTEXISTS_ERROR);
        }

        if (AccountStatus.CANCEL.name().equals(account.getStatus())) {
            log.info("账户信息查询：账户状态异常 ：{}", JsonUtils.toJsonString(account));
            throw new BizRuntimeException(HandlerResult.ACCOUNT_STATUS_ERROR);
        }

        String adjustDate = DateUtil.getDate("yyyyMMdd");
        adjustBill.setAdjustDate(adjustDate);
        adjustBill.setAuditStatus(AuditStatus.WAIT_AUDIT.name());
        adjustBill.setStatus(AdjustStatus.INIT.name());
        adjustBill.setAdjustedAmount(BigDecimal.ZERO);
        adjustBill.setUserRole(account.getUserRole());
        adjustBill.setOrigin(account.getOrigin());
        String transOrder = adjustBill.getUserNo() + DateUtil.getDate("yyMMddHHmmsss");
        adjustBill.setTransOrder(transOrder);
        accountAdjustService.insert(adjustBill);
        log.info("新增调账完成:{}", adjustBill);
    }

    public AccountAdjustBill findByTransOrder(String transOrder) {
        return accountAdjustService.findByTransOrder(transOrder);
    }

    public PageInfo<AccountAdjustBill> pageByParams(Map<String, String> params, int currentPage, int pageSize) {
        return accountAdjustService.pageByParams(params, currentPage, pageSize);
    }

    @Transactional(rollbackFor = Exception.class)
    public void auditAdjust(String transOrder, String operator, String auditStatus, String remark) {
        AccountAdjustBill adjustBill = accountAdjustService.findByTransOrder(transOrder);
        adjustBill.setAuditStatus(auditStatus);
        adjustBill.setRemark(remark);
        adjustBill.setOperator(operator);
        switch (AuditStatus.valueOf(auditStatus)) {
            case PASS:
                auditPass(adjustBill);
                break;
            case REJECT:
                auditReject(adjustBill);
                break;
            default:
                throw new ServerException("调账单不支持的审核状态");
        }
    }

    private AccountKeepReq convertToAccountKeepReq(AccountAdjustBill adjustBill) {
        AccountKeepReq keepReqBean = new AccountKeepReq();
        keepReqBean.setAccountType(AccountType.valueOf(adjustBill.getAccType()));
        keepReqBean.setOperator(adjustBill.getOperator());
        keepReqBean.setRemark(adjustBill.getRemark());
        keepReqBean.setRequestTime(new Date());
        keepReqBean.setSysSource(SysConstants.SYSTEM_SOURCE);
        keepReqBean.setUserNo(adjustBill.getUserNo());

        AccountTradeBean tradeBean = new AccountTradeBean();
        if (AdjustType.REDUCE.name().equals(adjustBill.getAdjustType())) {
            tradeBean.setBizType(BizType.ADJUST_SUBTRACT);
        } else {
            tradeBean.setBizType(BizType.ADJUST_PLUS);
        }
        tradeBean.setRemark(adjustBill.getReason());
        tradeBean.setTransAmt(adjustBill.getAmount().subtract(adjustBill.getAdjustedAmount()));
        tradeBean.setTransOrder(adjustBill.getTransOrder());
        tradeBean.setFundSymbol(AdjustType.valueOf(adjustBill.getAdjustType()).getFundSymbol());
        keepReqBean.setTradeBeans(Arrays.asList(tradeBean));
        return keepReqBean;
    }

    private void auditPass(AccountAdjustBill adjustBill) {
        AccountKeepReq keepReq = convertToAccountKeepReq(adjustBill);
        AccountKeepRes keepRes = accountKeepBiz.keepAccounts(keepReq);
        log.info("审核通过 调账返回: 【{}, {}】", keepReq, keepRes);
        adjustBill.setAuditStatus(AuditStatus.PASS.name());
        adjustBill.setAdjustedAmount(adjustBill.getAmount());
        adjustBill.setStatus(AdjustStatus.SUCCESS.name());
        adjustBill.setFinishDate(new Date());
        accountAdjustService.update(adjustBill);
        log.info("审核通过 调账成功: {}", adjustBill);
    }

    private void auditReject(AccountAdjustBill adjustBill) {
        adjustBill.setAuditStatus(AuditStatus.REJECT.name());
        adjustBill.setFinishDate(new Date());
        accountAdjustService.update(adjustBill);
        log.info("审核拒绝 调账成功: {}", adjustBill);
    }

}
