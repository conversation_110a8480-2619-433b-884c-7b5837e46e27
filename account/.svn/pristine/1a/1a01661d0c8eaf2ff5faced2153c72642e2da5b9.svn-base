package com.pay.account.channel.bean.req;

import com.pay.frame.common.base.enums.Origin;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * <AUTHOR> @date 2024/7/25
 * @apiNote
 */
@Data
@ToString(callSuper = true)
public class BalanceReq {

    /**
     * 付款账户类型
     *   01：营销管理账户
     *   02：商户付款账号
     */
    @NotEmpty(message = "付款账户类型不能为空")
    private String acsType;

    @NotEmpty(message = "品牌标识")
    private Origin origin;
}
