package com.pay.account.core.biz.remit;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.account.core.entity.WithdrawConfig;
import com.pay.account.core.service.remit.WithdrawConfigService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class WithdrawConfigBiz {
    @Resource
    private WithdrawConfigService withdrawConfigService;

    public PageInfo<WithdrawConfig> pageByParams(Map<String, Object> queryParams) {
        int currentPage = Integer.parseInt(queryParams.getOrDefault("currentPage", "1").toString());
        PageHelper.startPage(currentPage, 10);
        List<WithdrawConfig> configs = withdrawConfigService.listByParams(queryParams);
        PageInfo<WithdrawConfig> page = new PageInfo<>(configs);
        return page;
    }

    public void addConfig(WithdrawConfig withdrawConfig) {
        withdrawConfigService.insert(withdrawConfig);
    }

    public WithdrawConfig findById(Long id) {
        return withdrawConfigService.findById(id);
    }

    public void update(WithdrawConfig withdrawConfig) {
        withdrawConfigService.update(withdrawConfig);
    }

    public void updateStatus(long id, String status, String operator) {
        withdrawConfigService.updateStatus(id, status, operator);
    }
}
