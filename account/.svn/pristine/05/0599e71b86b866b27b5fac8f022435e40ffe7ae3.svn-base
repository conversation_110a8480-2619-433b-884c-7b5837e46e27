package com.pay.account.core.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.pay.account.core.entity.AccountManageHis;

public interface AccountManageHisMapper {

    int insert(AccountManageHis record);

    /**
     * 冪等查询判断数据是否已操作
     *
     * @param accNo
     * @param operType
     * @param requestFlow
     * @return
     */
    AccountManageHis selectByUniq(@Param("accNo") String accNo,
        @Param("operType") String operType,
        @Param("requestFlow") String requestFlow);

    /**
     * 根据账户编号查询未被解冻的冻结操作流水
     *
     * @param accNo
     * @return
     */
    List<String> selectRequestFlowForMinus(@Param("accNo") String accNo);

    /**
     * 指定流水查询账户操作记录
     * 
     * @param requestFlow
     * @return
     */
    List<AccountManageHis> selectByRequestFlow(@Param("requestFlow") String requestFlow);

    List<AccountManageHis> listByParams(Map<String, String> params);

    List<AccountManageHis> list4UnFreeze(Map<String, String> params);
}
