package com.pay.account.core.remote;

import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.pay.frame.common.base.bean.ResultsBean;

/**
 *
 */
@FeignClient(value = "BRAND-CHANNEL-CORE")
public interface BcConfigClient {

    /**
     * 验签
     * @param prNo
     * @param sign
     * @return
     */
    @RequestMapping(value = "/brand-channel-core/bcConfig/sign/checkBack", method = RequestMethod.POST)
    ResultsBean<Boolean> signCheckBack(@RequestParam("prNo") String prNo,
                                       @RequestParam("sign") String sign);


}
