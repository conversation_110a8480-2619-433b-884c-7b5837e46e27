package com.pay.account.core.service.account;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.pay.account.core.entity.AccountSnapshot;
import com.pay.account.core.mapper.AccountSnapshotMapper;

/**
 * <AUTHOR>
 * @date 2021-11-12 13:53:04
 **/
@Service
public class AccountSnapshotService {

    @Autowired
    private AccountSnapshotMapper accountSnapshotMapper;

    
    public void insert(AccountSnapshot record) {
        record.setCreateTime(new Date());
        accountSnapshotMapper.insert(record);
    }

    public AccountSnapshot selectById(Long id) {
        return accountSnapshotMapper.selectById(id);
    }

    
    public List<AccountSnapshot> statistAccountSnapshot(String snapshotDate) {
        return accountSnapshotMapper.statistAccountSnapshot(snapshotDate);
    }

    
    public int deleteBySnapshotDate(String snapshotDate) {
        return accountSnapshotMapper.deleteBySnapshotDate(snapshotDate);
    }
   
}
