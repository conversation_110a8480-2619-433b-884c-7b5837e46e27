package com.pay.account.core.remote.bean.settle.req;

import java.math.BigDecimal;
import java.util.List;

import org.hibernate.validator.constraints.NotEmpty;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> 、YS有
 * @date 2024/11/8
 * @apiNote
 */
@Data
@SuperBuilder
@ToString(callSuper = true)
public class SettleCustRemitReqDto {
	private String origin;
    /**
     * LS、YS
     * 商户编号
     */
    @NotEmpty(message = "通道方商户编号不能为空")
    private String thirdCustNo;

    /**
     * LS、YS
     * 出款请求单号
     */
    @NotEmpty(message = "出款请求单号不能为空")
    private String payOrder;

    /**
     * LS、YS
     * 	出款金额 单位：元
     */
    @NotEmpty(message = "出款金额 不能为空")
    private BigDecimal remitAmount;

    /**
     * YS
     * 渠道终端号
     */
    private String posCati;

    /**
     * YS
     * 收款方银行名称
     */
    private String headBankName;

    /**
     * YS
     * 收款方银行账户
     */
    private String bankAccountNo;

    /**
     * YS
     * 收款方账户名称
     */
    private String bankAccountName;

    /**
     * LS、YS
     * 结算信息 [{“settleNo”:”202410187485967152”,”settleAmt”:”45.36”}] settleNo：结算单号；settleAmt：结算金额
     */
    private List<SettleDetail> settleList;


    @Data
    public static class SettleDetail {
        /**
         * 结算单号
         */
        private String settleNo;
        /**
         * 结算金额
         */
        private BigDecimal settleAmt;
    }

}
