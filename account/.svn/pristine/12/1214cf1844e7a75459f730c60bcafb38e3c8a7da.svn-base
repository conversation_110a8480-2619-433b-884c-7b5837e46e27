package com.pay.account.core.dto.req.account;

import javax.validation.constraints.NotNull;

import com.pay.frame.common.base.enums.AccountOperType;
import com.pay.frame.common.base.enums.AccountType;

import lombok.Data;
import lombok.ToString;

@Data
@ToString(callSuper = true)
public class AccountManageReqBean extends BaseRequest {

    private static final long serialVersionUID = -7589055628609078672L;

    /**
     * 用户编号
     */
    private String userNo;
    
    private String userRole;

    /**
     * 账户类型
     */
    private AccountType accType;

    /**
     * 账户编号
     */
    private String accNo;

    /**
     * 支付密码
     */
    private String payPw;

    /**
     * 账户操作类型
     */
    @NotNull(message = "账户操作类型不能为空")
    private AccountOperType operType;

    /**
     * 请求流水
     */
    private String requestFlow;

    /**
     * 请求来源
     */
    private String origin;

}
