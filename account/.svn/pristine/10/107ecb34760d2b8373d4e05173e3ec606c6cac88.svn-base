package com.pay.account.channel.qxb.bean.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

import java.math.BigDecimal;

/**
 * 订单单笔申请
 *
 * <AUTHOR>
 *
 */
@Data
@ToString(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class QxbRemitReq extends QxbBaseReq {
    /**
     * 商户订单号（全局唯一，不可重复）
     */
    @NotBlank(message = "'商户订单号 '不能为空")
    private String orderNoOut;
    /**
     * 业务订单用途描述（代+商户名称+付个人经营所得额。例如：代北京百度网讯科技有限公司付个人经营所得额）
     */
    @NotBlank(message = "'业务订单用途描述 '不能为空")
    private String bizPurpose;
    @NotBlank(message = "'任务编号 '不能为空")
    @Length(min = 20, max = 20, message = "'任务编号 '长度必须是{max}")
    private String taskNo;
    /**
     * 任务结算金额（单位：元）
     */
    @NotBlank(message = "'任务结算金额 '不能为空")
    private BigDecimal wageAmount;
    /**
     * 客户账户号（银行卡号/支付宝账号）
     */
    @NotBlank(message = "'客户账户号 '不能为空")
    private String cardNo;
    @NotBlank(message = "'客户手机号 '不能为空")
    private String cardMobile;
}
