package com.pay.account.core.service.account;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.account.core.dto.rsp.account.AccountFundSumAmtRsp;
import com.pay.account.core.entity.AccountFundHis;
import com.pay.account.core.mapper.AccountFundHisMapper;
import com.pay.frame.common.base.exception.OptimisticException;

import lombok.extern.slf4j.Slf4j;

/**
 *
 *
 *<AUTHOR>
 *@date 2023-01-04 09:45:58
 **/
@Slf4j
@Service
public class AccountFundHisService {

    @Resource
    private AccountFundHisMapper accountFundHisMapper;

    public AccountFundHis findHisByUATBF(String userNo, String accType, String transOrder, String bizType, String fundSymbol) {
        return accountFundHisMapper.findHisByUATBF(userNo, accType, transOrder, bizType, fundSymbol);
    }

    public void insert(AccountFundHis fundHis) {
        int i = accountFundHisMapper.insert(fundHis);
        if (i != 1) {
            throw new OptimisticException("记录账户资金流水异常");
        }
    }


    public PageInfo<AccountFundHis> pageByParams(Map<String, String> params, int currentPage, int pageSize) {
        PageHelper.startPage(currentPage, pageSize);
        List<AccountFundHis> fundHis = accountFundHisMapper.listByParams(params);
        PageInfo<AccountFundHis> pageInfo = new PageInfo<>(fundHis);
        return pageInfo;
    }

	public AccountFundSumAmtRsp findAccFundSumAmt(Map<String, String> requestMap) {
		// TODO Auto-generated method stub
		return accountFundHisMapper.findAccFundSumAmt(requestMap);
	}

}
