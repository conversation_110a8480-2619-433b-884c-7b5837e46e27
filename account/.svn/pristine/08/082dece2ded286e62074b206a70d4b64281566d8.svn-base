package com.pay.account.core.enums;

/**
 * <AUTHOR>
 * @version [版本号, 2018年12月3日 下午10:01:31]
 * @Description 返回码定义
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
public enum HandlerRetCode {

    SUCCESS_CODE("000000", "SUCCESS"),
    UNKOWN_ERROR("999999", "系统未知错误"),
    PARAM_ERROR("900001", "参数错误"),
    USER_ROLE_ERROR("900002", "角色参数异常"),
    USER_NO_ERROR("900003", "用户参数异常"),
    ACCOUNT_EXISTS_CODE("100000", "账户已存在"),
    ACCOUNT_NOTEXISTS_ERROR("100001", "账户不存在"),
    ACCOUNT_STATUS_ERROR("100002", "账户状态异常"),
    ACCOUNT_SIGN_ERROR("100003", "账户签名验证失败"),
    ACCOUNT_BAL_LESSZERO_ERROR("100004", "账户余额小于0"),
    ACCOUNT_BAL_NOTENOUGH_ERROR("100005", "账户余额不足"),
    TRANSAMT_LESSEQZERO_ERROR("200001", "金额小于等于0"),
    FUND_SYMBOL_ERROR("200002", "资金方向不正确"),
    TRADE_ISNULL_ERROR("200003", "记账款项为空"),
    FUN_OPERTYPE_ERROR("200004", "资金操作类型错误"),
    FUN_BIZTYPE_ERROR("200004", "款项业务类型错误"),
    PAYPWD_EXIST_ERROR("300001", "支付密码已设置"),
    PAYPWD_NOTEXIST_ERROR("300002", "支付密码未设置"),
    PAYPWD_LAST_AS_ERROR("300003", "新密码和旧密码不能一致"),
    
    FREEZE_ERROR("400001", "账户冻结异常"),
    UNFREEZE_NOTFOUND_BY_FLOW("400002", "账户解冻异常,没找到对应的冻结流水"),
    ADJUST_BILL_ERROR("500001", "调账记录异常"),
    ADJUST_TYPE_ERROR("500002", "调账类型异常"),
    ADJUST_AUDIT_ERROR("500003", "调账审核处理异常"),
    ADJUST_INCRESE_ERROR("500004", "调增处理异常"),
    ADJUST_REDUCE_ERROR("500005", "调减处理异常"),
    ACCOUNT_INVOICE_ERROR("600000", "发票账户异常"),
    ;
    private String retCode;
    private String retMsg;

    private HandlerRetCode(String retCode, String retMsg) {
        this.retCode = retCode;
        this.retMsg = retMsg;
    }

    public String getRetCode() {
        return retCode;
    }

    public String getRetMsg() {
        return retMsg;
    }

}
