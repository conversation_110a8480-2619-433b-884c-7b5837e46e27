package com.pay.account.core.controller.remit;

import java.util.Map;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.pay.account.core.biz.remit.RemitChannelBiz;
import com.pay.account.core.dto.req.remit.AgentSettleCardReqBean;
import com.pay.account.core.entity.RemitChannelReport;
import com.pay.account.core.service.remit.RemitChannelReportService;
import com.pay.frame.common.base.bean.ResultsBean;

import lombok.extern.slf4j.Slf4j;

/**
 * 出款通道报备
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/remitChannelReport")
public class RemitChannelReportController {
    @Resource
    private RemitChannelReportService remitChannelReportService;
    
    @Resource
    private RemitChannelBiz remitChannelBiz;

    /**
     * 报备信息查询
     *
     * @param requestMap
     * @return
     */
    @RequestMapping(value = "/pageByParams")
    public ResultsBean<PageInfo<RemitChannelReport>> pageByParams(@RequestParam Map<String, String> requestMap) {
        log.info("报备信息查询列表参数{}", requestMap);
        PageInfo<RemitChannelReport> pageInfo = remitChannelReportService.pageByParams(requestMap);
        log.info("报备信息查询列表结果{}", pageInfo.getList());
        return ResultsBean.SUCCESS(pageInfo);
    }
    
    
    @RequestMapping(value = "/register")
    public ResultsBean<String> register(@RequestBody AgentSettleCardReqBean agentSettleCardReqBean) {
        log.info("报备信息查询列表参数{}", agentSettleCardReqBean);
        return remitChannelBiz.register(agentSettleCardReqBean);
    }
    
    
}
