package com.pay.account.core.exception;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

import com.pay.account.channel.leshua.exception.LSTransException;
import com.pay.account.channel.yinsheng.exception.YSTransException;
import com.pay.account.channel.zhongfu.exception.ZFTransException;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.pay.account.channel.qxb.exception.QxbException;
import com.pay.account.core.config.AlarmConfig;
import com.pay.account.core.remote.SmsClient;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.frame.common.base.util.JsonUtils;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {


    @Resource
    private AlarmConfig alarmConfig;

    @Resource
    private SmsClient smsClient;

    /**
     * 不支持的请求异常
     *
     * @param e
     * @return
     **/
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResultsBean httpRequestMethodNotSupportedExceptionHandler(HttpRequestMethodNotSupportedException e) {
        log.error("不支持的请求方法", e);
        return ResultsBean.FAIL("不支持' " + e.getMethod() + "'请求");
    }


    /**
     * Form 表单请求
     * JavaBean 请求参数异常
     *
     * @param e
     * @description
     **/
    @ExceptionHandler(BindException.class)
    public ResultsBean bindExceptionHandler(BindException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        List<String> collect = fieldErrors.stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.toList());
        log.error("form 请求数据绑定验证异常", e);
        return ResultsBean.FAIL(collect.get(0));
    }

    /**
     * 独立参数请求校验异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResultsBean constraintViolationExceptionHandler(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> constraintViolations = e.getConstraintViolations();
        List<String> collect = constraintViolations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.toList());
        log.error("参数直传 请求数据绑定验证异常", e);
        return ResultsBean.FAIL(collect.get(0));
    }

    /**
     * RequestBody为 json 的参数校验异常捕获
     *
     * @param e
     * @return
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultsBean methodArgumentNotValidExceptionHandler(MethodArgumentNotValidException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        List<String> collect = fieldErrors.stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.toList());
        log.error("application-json request body 请求数据绑定验证异常", e);
        return ResultsBean.FAIL(collect.get(0));
    }

    /**
     * @param ex
     * @return
     * @Description 定义异常处理
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public ResultsBean<Object> handlerException(Exception ex) {
        log.error("统一异常处理 {}", ex.toString(), ex);
        String message = "ACCOUNT-CORE" + ex.getMessage();
        message = message.length() > 200 ? message.substring(0, 200) : message;
        String requestNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
        sendDingDingMsg(requestNo, message);
        if (ex instanceof ServerException) {
            ServerException serverex = (ServerException) ex;
            return ResultsBean.FAIL(serverex.getMessage(), serverex.getCode());
        } else if (ex instanceof BizRuntimeException) {
            BizRuntimeException bizEx = (BizRuntimeException) ex;
            return ResultsBean.FAIL(bizEx.getErrMsg(), bizEx.getErrCode());
        } else if (ex instanceof QxbException) {
            QxbException qxbException = (QxbException) ex;
            return ResultsBean.FAIL(qxbException.getMessage(), qxbException.getErrCode());
        } else if (ex instanceof PwdNoNumException) {
            PwdNoNumException pwdNoNumException = (PwdNoNumException) ex;
            return ResultsBean.FAIL(pwdNoNumException.getMessage(), pwdNoNumException.getErrCode());
        } else if (ex instanceof ZFTransException) {
            ZFTransException zFTransException = (ZFTransException) ex;
            return ResultsBean.FAIL(zFTransException.getMessage(), zFTransException.getErrCode());
        }else if (ex instanceof YSTransException) {
            YSTransException ysTransException = (YSTransException) ex;
            return ResultsBean.FAIL(ysTransException.getMessage(), ysTransException.getErrCode());
        }else if (ex instanceof LSTransException) {
            LSTransException lsTransException = (LSTransException) ex;
            return ResultsBean.FAIL(lsTransException.getMessage(), lsTransException.getErrCode());
        } else {
            /** 系统异常 */
            return ResultsBean.EXCEPTION("系统异常");
        }
    }

    private void sendDingDingMsg(String requestNo, String content) {
        try {
            SmsClient.SendParams sendParams = new SmsClient.SendParams();
            sendParams.setRequestNo(requestNo);
            sendParams.setTemplateParam(JsonUtils.bean2Json(new TemplateParam(content)));
            sendParams.setTemplateCode(alarmConfig.getTemplateCore());
            ResultsBean<String> result = smsClient.sendSms(sendParams);
            log.info("Global send ding ding msg result {}, {}", sendParams, result);
        } catch (Exception e) {
            log.error("发送钉钉消息异常 {}", content, e);
        }
    }

    @Data
    private class TemplateParam {
        private String msg;

        public TemplateParam(String msg) {
            this.msg = msg;
        }
    }

}
