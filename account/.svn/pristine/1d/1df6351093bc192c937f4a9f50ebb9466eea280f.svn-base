package com.pay.account.channel.qxb.bean.res;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 零工认证
 *
 * <AUTHOR>
 *
 */
@Data
@ToString(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class QxbLaborAuthRes {

    @NotBlank(message = "'平台客户编号 '不能为空")
    private String customerNo;
}
