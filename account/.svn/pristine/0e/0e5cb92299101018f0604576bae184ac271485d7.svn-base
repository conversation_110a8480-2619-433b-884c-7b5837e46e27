package com.pay.account.core.task;


import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.pay.account.core.biz.remit.RemitBatchBiz;
import com.pay.account.core.entity.RemitBatch;
import com.pay.account.core.utils.DateUtil;
import com.pay.frame.common.base.enums.UserRole;
import com.pay.frame.common.base.enums.account.AuditStatus;
import com.pay.frame.common.base.enums.account.RemitBatchStatus;
import com.pay.frame.common.base.enums.account.WithdrawType;
import com.pay.job.common.handler.IJobHandler;
import com.pay.job.common.param.ReturnT;
import com.pay.job.executor.annotation.JobHandler;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@JobHandler("remitTransD1Task")
public class RemitTransD1Task extends IJobHandler {

    @Resource
    private RemitBatchBiz remitBatchBiz;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        try {
        	Map<String, String> params = new HashMap<String, String>();
        	params.put("status", RemitBatchStatus.INIT.name());
        	params.put("auditStatus",AuditStatus.WAIT_AUDIT.name());
        	params.put("businessCode", WithdrawType.TRANS_WITHDRAW.name());
        	List<RemitBatch> list = remitBatchBiz.listByParams(params);
        	for (RemitBatch remitBatch : list) {
        		 remitBatchBiz.passRemitBatch(Long.valueOf(remitBatch.getId()), UserRole.SYSTEM.name());
			}
        } catch (Exception e) {
            log.error("D1出款结算批次异常", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

}
