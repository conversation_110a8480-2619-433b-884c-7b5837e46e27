package com.pay.account.core.biz.remit;

import com.pay.account.channel.bean.req.RemitReq;
import com.pay.account.core.biz.account.SystemAccountBiz;
import com.pay.account.core.config.RemitChannelFactory;
import com.pay.account.core.dto.req.remit.CorpWithdrawReq;
import com.pay.account.core.dto.req.remit.WithdrawReq;
import com.pay.account.core.dto.rsp.remit.WithdrawRsp;
import com.pay.account.core.entity.RemitBill;
import com.pay.account.core.entity.RemitChannel;
import com.pay.account.core.entity.SettleCard;
import com.pay.account.core.entity.WithdrawRecord;
import com.pay.account.core.enums.ChannelPayableStrategy;
import com.pay.account.core.enums.HandlerResult;
import com.pay.account.core.exception.BizRuntimeException;
import com.pay.account.core.service.remit.RemitBillService;
import com.pay.account.core.service.remit.RemitChannelService;
import com.pay.account.core.utils.assembler.WithdrawAssembler;
import com.pay.frame.common.base.enums.SettleCardType;
import com.pay.frame.common.base.enums.account.RemitBillStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-17 13:37:32
 **/
@Slf4j
@Service
public class CorpWithdrawBiz {

    @Resource
    private WithdrawRecordBiz withdrawRecordBiz;

    @Resource
    private RemitBillBiz remitBillBiz;

    @Resource
    private RemitBillService remitBillService;

    @Resource
    private RemitChannelService remitChannelService;

    @Resource
    private SystemAccountBiz systemAccountBiz;
    @Resource
    private RemitChannelFactory remitChannelFactory;


    /**
     * 品牌方 - 提现
     * 1. 检查付款配置可否提现
     * 2. 生成提现单
     * 3. 账户记账
     * 4. 生成付款单
     */
    @Transactional(rollbackFor = Exception.class)
    public WithdrawRsp withDraw(CorpWithdrawReq corpWithdrawReq) {
        WithdrawReq withdrawReq = corpWithdrawReq.convReq();
        SettleCard bcSettleCard = buildBcSettleCard(corpWithdrawReq);
        WithdrawRecord withdrawRecord = withdrawRecordBiz.initWithdrawRecord(withdrawReq, bcSettleCard);
        RemitBill remitBill = remitBillBiz.initRemitBill(withdrawRecord);
        brandRemitSend(remitBill);
        WithdrawRsp withdrawRsp = WithdrawAssembler.toWithdrawRsp(withdrawRecord);
        return withdrawRsp;
    }


    /**
     * 构建 BC 品牌通道提现 结算卡对象
     * 品牌通道方，指定结算卡进行结算
     *
     * @param corpWithdrawReq
     * @return
     */
    private SettleCard buildBcSettleCard(CorpWithdrawReq corpWithdrawReq) {
        SettleCard bcSettleCard = new SettleCard();
        bcSettleCard.setCardType(SettleCardType.OPEN.name());
        bcSettleCard.setPrePhone(corpWithdrawReq.getPhoneNo());
        bcSettleCard.setIdentityNo(corpWithdrawReq.getIdentityNo());
        bcSettleCard.setBankName(corpWithdrawReq.getHeadBankName());
        bcSettleCard.setBankAccountName(corpWithdrawReq.getBankAccountName());
        bcSettleCard.setBankAccountNo(corpWithdrawReq.getBankAccountNo());
        return bcSettleCard;
    }

    public WithdrawRsp withDrawDetail(String transOrder) {
        WithdrawRecord withdrawRecord = withdrawRecordBiz.findByTransOrder(transOrder);
        if (withdrawRecord == null) {
            throw new BizRuntimeException(HandlerResult.REMIT_BILL_NULL_ERROR);
        }
        WithdrawRsp withdrawRsp = WithdrawAssembler.toWithdrawRsp(withdrawRecord);
        return withdrawRsp;
    }


    @Transactional(rollbackFor = Exception.class)
    public void audit(String transOrder, String operator) {
        log.info("corp withdraw audit 【{},{}】", transOrder, operator);
        StopWatch sw = new StopWatch("corpWithdraw audit");
        RemitBill remitBill = remitBillService.findByTransOrderForUpdate(transOrder);
        remitBill.setAuditOperator(operator);
        sw.start("checkAuditRemit");
        checkAuditRemit(remitBill);
        sw.stop();
        sw.start("checkAuditRemit");
        auditRemit(remitBill);
        sw.stop();
        log.info("corp withdraw audit 【{}, {}】", transOrder, operator, sw.prettyPrint());
    }

    private void auditRemit(RemitBill remitBill) {
        remitBill.setStatus(RemitBillStatus.SEND.name());
        remitBillBiz.updateById(remitBill);
    }


    private RemitReq buildRemitReq(RemitBill remitBill) {
        RemitReq remitReq = RemitReq.builder()
                .remitAmount(remitBill.getRemitAmount())
                .transOrder(remitBill.getTransOrder())
                .bankAccountNo(remitBill.getBankAccountNo())
                .bankAccountName(remitBill.getBankAccountName())
                .channelCustNo(remitBill.getChannelUserNo())
                .cardType(remitBill.getCardType())
                .sendTime(remitBill.getTxnDate())
                .channelTransOrder(remitBill.getTxnNo())
                .channelOrderRemark(remitBill.getRemark())
                .idNo(remitBill.getIdentityNo())
                .bankName(remitBill.getBankName())
                .phoneNo(remitBill.getPhoneNo())
                .transFee(remitBill.getTradeFee())
                .remitFee(remitBill.getFoFee())
                .digAmt(remitBill.getDiqAmt())
                .esimFlg(remitBill.getEsimFlg())
                .transFeeRate(remitBill.getTradeFeeRate())
                .origin(remitBill.getOrigin())
                .posSN(remitBill.getPosSn())
                .build();
        return remitReq;
    }


    private void brandRemitSend(RemitBill remitBill) {
        List<RemitChannel> remitChannels = remitChannelService.findByBO(remitBill.getBusinessCode(), remitBill.getOrigin().name());
        if (CollectionUtils.isEmpty(remitChannels)) {
            throw new BizRuntimeException(HandlerResult.REMIT_CHANNEL_NO_ERROR);
        }
        String txnNo = remitBillBiz.getTxnNo(8);
        RemitChannel remitChannel = remitChannels.get(0);
        remitBill.setChannelNo(remitChannel.getChannelNo());
        remitBill.setTxnNo(txnNo);
        remitBill.setStatus(RemitBillStatus.DOING.name());
        boolean canPayable = ChannelPayableStrategy.valueOf(
                remitBill.getChannelNo()).canPayable(systemAccountBiz, remitBill);
        if (!canPayable) {
            log.info("不支持账户的提现：{}", remitBill);
            throw new BizRuntimeException("不支持提现的订单: " + remitBill.getUserNo());
        }
        remitBillBiz.updateById(remitBill);
    }

    private static void checkAuditRemit(RemitBill exist) {
        if (exist == null) {
            throw new BizRuntimeException(HandlerResult.REMIT_BILL_NULL_ERROR);
        }

        if (exist.getRemitAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new BizRuntimeException(HandlerResult.REMIT_BILL_AMOUNT_ERROR);
        }

        if (!RemitBillStatus.DOING.name().equals(exist.getStatus())) {
            throw new BizRuntimeException(HandlerResult.REMIT_BILL_STATUS_ERROR);
        }
    }

    public void cancel(String transOrder, String operator) {
        log.info("corp withdraw cancel 【{},{}】", transOrder, operator);
        StopWatch sw = new StopWatch("corpWithdraw cancel");
        RemitBill remitBill = remitBillService.findByTransOrderForUpdate(transOrder);
        remitBill.setAuditOperator(operator);
        sw.start("checkAuditRemit");
        checkAuditRemit(remitBill);
        sw.stop();
        sw.start("cancelWithdraw");
        cancelWithdraw(remitBill);
        sw.stop();
        log.info("corp withdraw cancel 【{}, {}】", transOrder, operator, sw.prettyPrint());

    }

    private void cancelWithdraw(RemitBill remitBill) {
        remitBill.setStatus(RemitBillStatus.REJECT.name());
        remitBillBiz.updateById(remitBill);
    }
}
