package com.pay.account.core.controller.remit;

import java.util.Date;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.pay.account.core.biz.remit.WithdrawConfigBiz;
import com.pay.account.core.entity.WithdrawConfig;
import com.pay.frame.common.base.bean.ResultsBean;

import lombok.extern.slf4j.Slf4j;

/**
 * 提现配置
 *
 * <AUTHOR>
 * @date 2023-03-17 10:47:40
 **/
@Slf4j
@RestController
@RequestMapping(value = "/withdrawConfig")
public class WithdrawConfigController {

    @Resource
    private WithdrawConfigBiz withdrawConfigBiz;

    /**
     * @param queryParams
     * @return
     * @Description: 分页查询
     */
    @RequestMapping(value = "/pageByParams", method = RequestMethod.POST)
    public ResultsBean<PageInfo<WithdrawConfig>> pageByParams(@RequestBody Map<String, Object> queryParams) {
        log.info("分页查询参数:{}", queryParams);
        PageInfo<WithdrawConfig> page = withdrawConfigBiz.pageByParams(queryParams);
        log.info("分页查询结果:{}", page);
        return ResultsBean.SUCCESS(page);
    }

    /**
     * @param remitConfigBean
     * @return
     * @Description: 新增
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public ResultsBean<String> add(@RequestBody WithdrawConfig remitConfigBean) {
        log.info("提现配置-新增 参数:{}", remitConfigBean);
        WithdrawConfig withdrawConfig = new WithdrawConfig();
        BeanUtils.copyProperties(remitConfigBean, withdrawConfig);
        withdrawConfig.setCreateTime(new Date());
        withdrawConfig.setOptimistic(0L);
        withdrawConfigBiz.addConfig(withdrawConfig);
        return ResultsBean.SUCCESS();
    }

    /**
     * @param id
     * @param status
     * @return
     * @Description: 状态修改
     */
    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    public ResultsBean<String> updateStatus(@RequestParam("id") long id,
                                            @RequestParam("status") String status,
                                            @RequestParam("operator") String operator) {
        log.info("提现配置-状态变更 参数:id={},status={},operator={}", id, status, operator);
        withdrawConfigBiz.updateStatus(id, status, operator);
        log.info("提现配置-状态变更完成。");
        return ResultsBean.SUCCESS();
    }

    /**
     * @param remitConfigBean
     * @return
     * @Description: 更新提现配置
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResultsBean<String> update(@RequestBody WithdrawConfig remitConfigBean) {
        log.info("提现配置-更新提现配置 参数: {} ", remitConfigBean);
        withdrawConfigBiz.update(remitConfigBean);
        log.info("提现配置-更新提现配置");
        return ResultsBean.SUCCESS();
    }


    /**
     * @param id
     * @return
     * @Description: 根据ID查询
     */
    @RequestMapping(value = "/findById", method = RequestMethod.POST)
    public ResultsBean<WithdrawConfig> findById(@RequestParam("id") Long id) {
        log.info("提现配置-根据ID查询 参数:{}", id);
        WithdrawConfig withdrawConfig = withdrawConfigBiz.findById(id);
        log.info("提现配置-根据ID查询返回:{}", withdrawConfig);
        return ResultsBean.SUCCESS(withdrawConfig);
    }
}
