package com.pay.account.core.dto.req.account;

import java.util.List;

import javax.validation.constraints.NotNull;

import org.hibernate.validator.constraints.NotBlank;

import com.pay.frame.common.base.enums.AccountOperType;
import com.pay.frame.common.base.enums.AccountType;
import com.pay.frame.common.base.enums.account.Currency;

import lombok.Data;

@Data
public class BatchAccountManageReqBean extends BaseRequest {

    private static final long serialVersionUID = -7589055628609078672L;

    /**
     * 用户编号
     */
    @NotBlank
    private String userNo;

    /**
     * 账户类型
     */
    @NotNull
    private List<AccountType> accTypes;

    /**
     * 币种
     */
    @NotNull
    private Currency currency = Currency.CNY;

    /**
     * 支付密码
     */
    @NotBlank
    private String payPw;

    /**
     * 操作类型
     */
    private AccountOperType operType;

    /**
     * 请求流水
     */
    @NotBlank
    private String requestFlow;

}
