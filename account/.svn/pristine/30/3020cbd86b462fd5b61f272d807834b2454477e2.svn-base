<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.account.core.mapper.RemitBatchMapper">
    <resultMap id="BaseResultMap" type="com.pay.account.core.entity.RemitBatch">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optimistic"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="CHANNEL_NO" jdbcType="VARCHAR" property="channelNo"/>
        <result column="TOTAL_COUNT" jdbcType="DECIMAL" property="totalCount"/>
        <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="SETTLE_CYCLE" jdbcType="VARCHAR" property="settleCycle"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="OPERATOR" jdbcType="VARCHAR" property="operator"/>
        <result column="AUDITOR" jdbcType="VARCHAR" property="auditor"/>
        <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime"/>
        <result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode"/>
        <result column="RESP_CODE" jdbcType="VARCHAR" property="respCode"/>
        <result column="RESP_MSG" jdbcType="VARCHAR" property="respMsg"/>
        <result column="AUDIT_STATUS" jdbcType="VARCHAR" property="auditStatus"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!-- -->
        ID, OPTIMISTIC, CREATE_TIME, UPDATE_TIME, CHANNEL_NO, TOTAL_COUNT, TOTAL_AMOUNT,
        SETTLE_CYCLE, STATUS, OPERATOR, AUDITOR, SEND_TIME, BUSINESS_CODE, RESP_CODE,
        RESP_MSG, AUDIT_STATUS
    </sql>
    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ZF_OPER_ADM.REMIT_BATCH
        where ID = #{id,jdbcType=DECIMAL}
    </select>
    <select id="findByIdLock" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ZF_OPER_ADM.REMIT_BATCH
        where ID = #{id,jdbcType=DECIMAL}
        for update
    </select>
    <select id="listByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ZF_OPER_ADM.REMIT_BATCH
        <where>
            <if test="queryParams.status != null and queryParams.status != '' ">
                and STATUS = #{queryParams.status,jdbcType=VARCHAR}
            </if>
            <if test="queryParams.auditStatus != null and queryParams.auditStatus != '' ">
                and AUDIT_STATUS = #{queryParams.auditStatus,jdbcType=VARCHAR}
            </if>
            <if test="queryParams.channelNo != null and queryParams.channelNo != '' ">
                and CHANNEL_NO = #{queryParams.channelNo,jdbcType=VARCHAR}
            </if>
            <if test="queryParams.operator != null and queryParams.operator != '' ">
                and OPERATOR = #{queryParams.operator,jdbcType=VARCHAR}
            </if>
            <if test="queryParams.auditor != null and queryParams.auditor != '' ">
                and AUDITOR = #{queryParams.auditor,jdbcType=VARCHAR}
            </if>
            <if test="queryParams.businessCode != null and queryParams.businessCode != '' ">
                and BUSINESS_CODE = #{queryParams.businessCode,jdbcType=VARCHAR}
            </if>
            <if test="queryParams.createTimeStart != null and queryParams.createTimeStart != '' ">
                AND CREATE_TIME &gt;= to_date(#{queryParams.createTimeStart} || ' 00:00:00','yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="queryParams.createTimeEnd != null and queryParams.createTimeEnd != '' ">
                AND CREATE_TIME &lt;= to_date(#{queryParams.createTimeEnd} || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="queryParams.sendTimeStart != null and queryParams.sendTimeStart != '' ">
                AND SEND_TIME &gt;= to_date(#{queryParams.sendTimeStart} || ' 00:00:00','yyyy-mm-dd hh24:mi:ss')
            </if>
            <if test="queryParams.sendTimeEnd != null and queryParams.sendTimeEnd != '' ">
                AND SEND_TIME &lt;= to_date(#{queryParams.sendTimeEnd} || ' 23:59:59','yyyy-mm-dd hh24:mi:ss')
            </if>
        </where>
        order by CREATE_TIME desc
    </select>
    <insert id="insert" parameterType="com.pay.account.core.entity.RemitBatch">
        <selectKey order="BEFORE" keyProperty="id" resultType="long">
            select ZF_OPER_ADM.SEQ_REMIT_BATCH_ID.nextval from DUAL
        </selectKey>
        insert into ZF_OPER_ADM.REMIT_BATCH (ID, OPTIMISTIC, CREATE_TIME,
        UPDATE_TIME, CHANNEL_NO, TOTAL_COUNT,
        TOTAL_AMOUNT, SETTLE_CYCLE, STATUS,
        OPERATOR, AUDITOR, SEND_TIME,
        BUSINESS_CODE, RESP_CODE, RESP_MSG,
        AUDIT_STATUS)
        values (#{id,jdbcType=DECIMAL}, #{optimistic,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP},
        #{updateTime,jdbcType=TIMESTAMP}, #{channelNo,jdbcType=VARCHAR}, #{totalCount,jdbcType=DECIMAL},
        #{totalAmount,jdbcType=DECIMAL}, #{settleCycle,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
        #{operator,jdbcType=VARCHAR}, #{auditor,jdbcType=VARCHAR},#{sendTime,jdbcType=TIMESTAMP},
        #{businessCode,jdbcType=VARCHAR}, #{respCode,jdbcType=VARCHAR}, #{respMsg,jdbcType=VARCHAR},
        #{auditStatus,jdbcType=VARCHAR})
    </insert>
    <update id="updateById" parameterType="com.pay.account.core.entity.RemitBatch">
        update ZF_OPER_ADM.REMIT_BATCH
        set OPTIMISTIC    = OPTIMISTIC + 1,
            UPDATE_TIME   = #{updateTime,jdbcType=TIMESTAMP},
            CHANNEL_NO    = #{channelNo,jdbcType=VARCHAR},
            TOTAL_COUNT   = #{totalCount,jdbcType=DECIMAL},
            TOTAL_AMOUNT  = #{totalAmount,jdbcType=DECIMAL},
            SETTLE_CYCLE  = #{settleCycle,jdbcType=VARCHAR},
            STATUS        = #{status,jdbcType=VARCHAR},
            AUDIT_STATUS  = #{auditStatus,jdbcType=VARCHAR},
            OPERATOR      = #{operator,jdbcType=VARCHAR},
            AUDITOR       = #{auditor,jdbcType=VARCHAR},
            SEND_TIME     = #{sendTime,jdbcType=TIMESTAMP},
            BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
            RESP_CODE     = #{respCode,jdbcType=VARCHAR},
            RESP_MSG      = #{respMsg,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=DECIMAL}
          and OPTIMISTIC = #{optimistic,jdbcType=DECIMAL}
    </update>
</mapper>