package com.pay.account.channel.bean.res;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;

/**
 *
 *
 *<AUTHOR>
 *@date 2023-03-08 10:51:27
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@ToString(callSuper=true)
public class RemitRes extends BaseRes {

    /**
     * 系统订单流水号
     */
    private String transOrder;

    /**
     * 付款账户姓名
     */
    private String bankAccountName;

    /**
     * 付款账户卡号
     */
    private String bankAccountNo;

    /**
     * 付款金额
     */
    private BigDecimal remitAmount;

    /**
     * 付款通道 流水号
     */
    private String channelTransOrder;
}
