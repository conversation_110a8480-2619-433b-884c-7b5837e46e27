package com.pay.account.core.biz.remit;


import java.math.BigDecimal;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.pay.account.core.dto.rsp.remit.WithdrawConfigBo;
import com.pay.account.core.dto.rsp.remit.WithdrawFeeBo;
import com.pay.account.core.enums.HandlerResult;
import com.pay.account.core.enums.RoundType;
import com.pay.account.core.exception.BizRuntimeException;
import com.pay.account.core.utils.BillingUtils;
import com.pay.frame.common.base.enums.account.WithdrawType;

import lombok.extern.slf4j.Slf4j;

/**
 * 提现计费业务类
 *
 * <AUTHOR>
 * @date 2023-03-17 14:34:56
 **/
@Slf4j
@Service
public class WithdrawFeeBiz {

    @Resource
    private WithdrawLimitBiz withdrawLimitBiz;

    public WithdrawFeeBo calcFee(String origin, String userNo, WithdrawType withdrawType, BigDecimal withdrawAmount) {
        WithdrawConfigBo withdrawConfig = withdrawLimitBiz.checkOutWithdrawConfig(origin, userNo, withdrawType);
        if (null == withdrawConfig) {
            log.error("预算手续费，提现配置为空 {}", withdrawConfig);
            throw new BizRuntimeException(HandlerResult.WITHDRAW_CONFIG_ERROR.getRetCode(),
                    HandlerResult.WITHDRAW_CONFIG_ERROR.getRetMsg());
        }

        BigDecimal rateFee =
                BillingUtils.computeFee(withdrawAmount, withdrawConfig.getRate(), RoundType.UP, 2);
        BigDecimal withdrawFee = BillingUtils.computeFee(withdrawAmount,
                withdrawConfig.getRate(), withdrawConfig.getExtAmt(), RoundType.UP, withdrawConfig.getFeeType());

        BigDecimal remitAmount = BillingUtils.subtract(withdrawAmount, withdrawFee);

        if (remitAmount.compareTo(withdrawAmount) > 0) {
            throw new BizRuntimeException(HandlerResult.REMIT_WITHDRAW_FEE_ERROR);
        }

        if (remitAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new BizRuntimeException(HandlerResult.REMIT_WITHDRAW_FEE_ERROR);
        }
        BigDecimal agentCashbackAmt = withdrawConfig.getExtAmt().subtract(withdrawConfig.getCashbackCost());
        agentCashbackAmt = BigDecimal.ZERO.compareTo(agentCashbackAmt) >= 0 ? BigDecimal.ZERO : agentCashbackAmt;
        return WithdrawFeeBo.builder()
                .amount(withdrawAmount)
                .rate(withdrawConfig.getRate())
                .rateFee(rateFee)
                .fixFee(withdrawConfig.getExtAmt())
                .totalFee(withdrawFee)
                .remitAmount(remitAmount)
                .cashbackCost(withdrawConfig.getCashbackCost())
                .channelCost(withdrawConfig.getChannelCost())
                .build();
    }
}
