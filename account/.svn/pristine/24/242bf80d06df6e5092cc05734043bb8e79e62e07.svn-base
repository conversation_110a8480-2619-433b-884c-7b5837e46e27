package com.pay.account.core.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

import org.apache.commons.lang3.StringUtils;

import com.pay.account.core.entity.Account;
import com.pay.frame.common.base.enums.AccountType;

import lombok.extern.slf4j.Slf4j;

/**
 *
 * @Description 账户操作工具类
 * <AUTHOR>
 * @version [版本号, 2018年12月3日 下午8:10:31]
 * @see  [相关类/方法]
 * @since [产品/模块版本]
 */
@Slf4j
public final class AccountUtils {


    private AccountUtils() {
    }

    /**
     *
     * @Description b1+b2 保留2位精度
     * @param b1
     * @param b2
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static BigDecimal add(BigDecimal b1, BigDecimal b2) {
        return b1.add(b2).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     *
     * @Description b1-b2 保留2位精度
     * @param b1
     * @param b2
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static BigDecimal substract(BigDecimal b1, BigDecimal b2) {
        return b1.subtract(b2).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 账户签名
     *
     * @param account 账户信息
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static String signature(Account account) {
        if (null == account) {
            return null;
        }

        String accNo = account.getAccNo();
        if (StringUtils.isBlank(accNo)) {
            throw new IllegalArgumentException();
        }
        // 盐值
        String salt = new StringBuilder(accNo).reverse().toString();
        String sourceSign = new StringBuilder(account.getUserNo())
                .append(account.getAccType()).append(account.getBalance().setScale(2)).append(salt).toString();
        String targetSign = EncryptUtils.MD5(sourceSign);
        log.info("账户签名 :account :[{}] ,sign :[{}]", account, targetSign);
        return targetSign;
    }

    /**
     *
     * @Description 账户编号生成：商户号 + 角色类型和账户类型（1位）+ 1位
     * @param userNo
     * @param userRole
     * @param accountType
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static String generateAccNo(String userNo, AccountType accountType) {
        StringBuilder suffix = new StringBuilder(userNo);
        suffix.append(accountType.getSuffix());
        return suffix.toString();
    }

    /**
     *
     * @Description 账户编号生成：毫秒时间戳反转加一定位数
     * @param seqAcc 序列
     * @param len 补充位数
     * @return 账户编号
     * @see [类、类#方法、类#成员]
     */
    @Deprecated
    public static String procAccNo(String seqAcc, int len) {
        StringBuilder prefix = new StringBuilder(DateUtil.getDateRandom()).reverse();
        String accNo = seqAcc;
        int seqLen = seqAcc.length();
        if (seqLen <= len) {
            // 位数不足，左补齐0
            accNo = prefix.append(String.format("%0" + len + "d", Integer.parseInt(seqAcc))).toString();
        } else {
            // 位数超出，截取
            accNo = prefix.append(seqAcc.substring(seqLen - len)).toString();
        }
        return accNo;
    }

    /**
     *
     * @Description 支付密码加密
     * @param payPw 支付密码
     * @param userNo 用户号反转作为盐
     * @return
     * @see [类、类#方法、类#成员]
     */
    public static String signPwd(String payPw, String userNo) {
        if (StringUtils.isNotBlank(payPw)) {
            String salt = new StringBuilder(userNo).reverse().toString();
            String sourceSign = payPw + salt;
            return EncryptUtils.MD5(sourceSign);
        }
        return payPw;
    }

    /**
     * @Description 验证账号是否有效
     * @param account 待验证账号
     * @return 如果有效返回true，如果无效返回false，如果account等于null，返回false
     * @see
     */
    public static boolean isValid(Account account) {
        if (account.getSignature() == null || "".equals(account.getSignature())) {
            return true;
        }
        return signature(account).equals(account.getSignature());
    }


}
