package com.pay.account.core.service.remit;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.pay.account.core.entity.WithdrawConfig;
import com.pay.account.core.mapper.WithdrawConfigMapper;
import com.pay.frame.common.base.enums.account.WithdrawType;
import com.pay.frame.common.base.exception.OptimisticException;
import com.pay.frame.common.base.exception.ServerException;

@Service
public class WithdrawConfigService {
    @Resource
    private WithdrawConfigMapper withdrawConfigMapper;

    public WithdrawConfig findToSortArticle(String origin,
                                            String week,
                                            String userNo,
                                            WithdrawType businessCode) {
        WithdrawConfig withdrawConfig = withdrawConfigMapper.findToSortArticle(origin, week, userNo, businessCode);
        return withdrawConfig;
    }


    /**
     * @param withdrawConfig 付款配置
     * @Description: 新增
     */
    public void insert(WithdrawConfig withdrawConfig) {
        withdrawConfig.setCreateTime(new Date());
        withdrawConfig.setOptimistic(0L);
        int i = withdrawConfigMapper.insert(withdrawConfig);
        if (i != 1) {
            throw new OptimisticException("新增提现配置异常");
        }
    }


    public void updateStatus(long id, String status, String operator) {
        WithdrawConfig exist = withdrawConfigMapper.findById(id);
        if (exist == null) {
            throw new ServerException("提现配置不存在");
        }
        exist.setStatus(status);
        exist.setOperator(operator);
        int i = withdrawConfigMapper.updateStatus(exist);
        if (i != 1) {
            throw new OptimisticException("update status by id optimistic error: " + id);
        }
    }

    public void update(WithdrawConfig withdrawConfig) {
        withdrawConfig.setUpdateTime(new Date());
        int i = withdrawConfigMapper.update(withdrawConfig);
        if (i != 1) {
            throw new OptimisticException("update by id optimistic error: " + withdrawConfig.getId());
        }
    }

    public WithdrawConfig findById(Long id) {
        return withdrawConfigMapper.findById(id);
    }

    public List<WithdrawConfig> listByParams(Map<String, Object> queryParams) {
        return withdrawConfigMapper.listByParams(queryParams);
    }
}
