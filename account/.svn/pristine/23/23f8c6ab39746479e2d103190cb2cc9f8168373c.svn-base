package com.pay.account.channel.leshua.assembler;


import com.pay.account.channel.bean.req.BalanceReq;
import com.pay.account.channel.bean.req.RemitQueryReq;
import com.pay.account.channel.bean.req.RemitReq;
import com.pay.account.channel.bean.res.BalanceRes;
import com.pay.account.channel.bean.res.RemitQueryRes;
import com.pay.account.channel.bean.res.RemitRes;
import com.pay.account.core.remote.bean.proxyPay.req.AcsBalanceQryReqDto;
import com.pay.account.core.remote.bean.proxyPay.req.FundoutProxyQryReqDto;
import com.pay.account.core.remote.bean.proxyPay.req.FundoutProxyReqDto;
import com.pay.account.core.remote.bean.proxyPay.res.AcsBalanceQryRspDto;
import com.pay.account.core.remote.bean.proxyPay.res.FundoutProxyQryRspDto;
import com.pay.account.core.remote.bean.proxyPay.res.FundoutProxyRspDto;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class LSAssembler {


    /**
     * 付款账户类型：01：营销管理账户 02：商户付款账号
     */
    private String acsType;

    public FundoutProxyReqDto toRemitReq(RemitReq remitReq) {
        FundoutProxyReqDto fundoutProxyReqDto = FundoutProxyReqDto.builder()
                .outTradeNo(remitReq.getChannelTransOrder())
                .phoneNo(remitReq.getPhoneNo())
                .name(remitReq.getBankAccountName())
                .idCardNo(remitReq.getIdNo().toUpperCase())
                .bankAcctId(remitReq.getBankAccountNo())
                .amount(remitReq.getRemitAmount())
                .accountType(acsType)
                .headBankCode(remitReq.getHeadBankCode())
                .headBankName(remitReq.getBankName())
                .alliedBankName(remitReq.getAlliedBankName())
                .alliedBankCode(remitReq.getAlliedBankCode())
                .cityName(remitReq.getBankCity())
                .idCardFrontURL(remitReq.getIdCardFrontURL())
                .idCardBackURL(remitReq.getIdCardBackURL())
                .remark(remitReq.getChannelOrderRemark())
                .build();
        return fundoutProxyReqDto;
    }

    public RemitRes toRemitRes(FundoutProxyRspDto fundoutProxyRspDto) {
        RemitRes remitRes = RemitRes.builder()
                .build();
        return remitRes;
    }

    public FundoutProxyQryReqDto toRemitQuery(RemitQueryReq remitQueryReq) {
        FundoutProxyQryReqDto YSPayFundoutProxyQryReqDto = FundoutProxyQryReqDto.builder().
                outTradeNo(remitQueryReq.getChannelTransOrder())
                .build();
        return YSPayFundoutProxyQryReqDto;
    }

    public RemitQueryRes toRemitQueryRes(FundoutProxyQryRspDto YSPayFundoutProxyQryRspDto) {
        RemitQueryRes remitQueryRes = RemitQueryRes.builder()
                .channelResMsg(YSPayFundoutProxyQryRspDto.getMessage())
                .remitStatus(YSPayFundoutProxyQryRspDto.getPayStatus())
                .channelResCode(YSPayFundoutProxyQryRspDto.getCode())
                .remitAmount(YSPayFundoutProxyQryRspDto.getAmount())
                .bankAccountName(YSPayFundoutProxyQryRspDto.getName())
                .bankAccountNo(YSPayFundoutProxyQryRspDto.getBankAcctId())
                .build();
        return remitQueryRes;
    }

    public AcsBalanceQryReqDto toBalanceQuery(BalanceReq balanceReq) {
        AcsBalanceQryReqDto acsBalanceQryReqDto = AcsBalanceQryReqDto.builder()
                .acsType(balanceReq.getAcsType())
                .build();
        return acsBalanceQryReqDto;
    }

    public BalanceRes toBalanceQueryRes(AcsBalanceQryRspDto YSPayAcsBalanceQryRspDto) {
        BalanceRes balanceRes = BalanceRes.builder().balance(YSPayAcsBalanceQryRspDto.getBalance()).build();
        return balanceRes;
    }


}
