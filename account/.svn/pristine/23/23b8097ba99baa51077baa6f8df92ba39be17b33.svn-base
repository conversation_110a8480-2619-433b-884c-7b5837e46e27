package com.pay.account.channel.qxb.bean.req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 零工认证
 *
 * <AUTHOR>
 *
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class QxbLaborAuthReq extends QxbBaseReq {

    @NotBlank(message = "'客户姓名 '不能为空")
    private String customerName;

    @NotBlank(message = "'手机号码 '不能为空")
    private String customerMobile;

    @NotBlank(message = "'身份证号码 '不能为空")
    private String customerPid;
    /**
     * 客户账户号（需结算至银行卡，则必填）
     */
    private String cardNo;

}
