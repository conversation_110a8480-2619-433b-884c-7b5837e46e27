<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.account.core.mapper.AccountSnapshotMapper">
    <resultMap id="BaseResultMap" type="com.pay.account.core.entity.AccountSnapshot">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="SNAPSHOT_DATE" jdbcType="VARCHAR" property="snapshotDate"/>
        <result column="SNAPSHOT_MONTH" jdbcType="VARCHAR" property="snapshotMonth"/>
        <result column="USER_NO" jdbcType="VARCHAR" property="userNo"/>
        <result column="USER_ROLE" jdbcType="VARCHAR" property="userRole"/>
        <result column="ACC_NO" jdbcType="VARCHAR" property="accNo"/>
        <result column="ACC_TYPE" jdbcType="VARCHAR" property="accType"/>
        <result column="BIZ_TYPE" jdbcType="VARCHAR" property="bizType"/>
        <result column="PLUS" jdbcType="DECIMAL" property="plus"/>
        <result column="SUBTRACT" jdbcType="DECIMAL" property="subtract"/>
        <result column="PRE_BAL" jdbcType="DECIMAL" property="preBal"/>
        <result column="CURR_BAL" jdbcType="DECIMAL" property="currBal"/>
        <result column="PRE_BAL" jdbcType="DECIMAL" property="preBal"/>
        <result column="FLOW_START_ID" jdbcType="DECIMAL" property="flowStartId"/>
        <result column="FLOW_END_ID" jdbcType="DECIMAL" property="flowEndId"/>
        <result column="ORIGIN" jdbcType="VARCHAR" property="origin"/>
    </resultMap>
    <sql id="Base_Column_List">
        ID, CREATE_TIME, SNAPSHOT_DATE, SNAPSHOT_MONTH, USER_NO, USER_ROLE, ACC_NO, ACC_TYPE, BIZ_TYPE,
        PLUS, SUBTRACT, PRE_BAL, CURR_BAL,FLOW_START_ID,FLOW_END_ID, ORIGIN
    </sql>
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ZF_OPER_ADM.ACCOUNT_SNAPSHOT
        where ID = #{id,jdbcType=DECIMAL}
    </select>
    <select id="statistAccountSnapshot" resultType="com.pay.account.core.entity.AccountSnapshot">
        SELECT ACC_NO, ACC_TYPE, USER_NO, USER_ROLE, PRE_BAL, CURR_BAL, SUBTRACT, SUBTRACT_NUM,
        PLUS, PLUS_NUM,FLOW_START_ID,FLOW_END_ID,
        BIZ_TYPE, #{snapshotDate,jdbcType=VARCHAR} AS SNAPSHOT_DATE,
        SUBSTR(#{snapshotDate,jdbcType=VARCHAR},1,7) AS SNAPSHOT_MONTH, ORIGIN
        FROM(
        SELECT ID, ACC_NO, ACC_TYPE, USER_NO, USER_ROLE, BIZ_TYPE,ORIGIN,
        SUM(DECODE(FUND_SYMBOL, 'SUBTRACT', -TRANS_AMT, 0))OVER(PARTITION BY ACC_NO, BIZ_TYPE) SUBTRACT,
        SUM(DECODE(FUND_SYMBOL, 'SUBTRACT', 1, 0))OVER(PARTITION BY ACC_NO, BIZ_TYPE) SUBTRACT_NUM,
        SUM(DECODE(FUND_SYMBOL, 'PLUS', TRANS_AMT, 0))OVER(PARTITION BY ACC_NO, BIZ_TYPE) PLUS,
        SUM(DECODE(FUND_SYMBOL, 'PLUS', 1, 0))OVER(PARTITION BY ACC_NO, BIZ_TYPE) PLUS_NUM,
        FIRST_VALUE(PRE_BAL)OVER(PARTITION BY ACC_NO ORDER BY ID) PRE_BAL,
        FIRST_VALUE(CURR_BAL)OVER(PARTITION BY ACC_NO ORDER BY ID DESC) CURR_BAL,
        FIRST_VALUE(ID)OVER(PARTITION BY ACC_NO, BIZ_TYPE  ORDER BY ID) FLOW_START_ID,
        FIRST_VALUE(ID)OVER(PARTITION BY ACC_NO, BIZ_TYPE  ORDER BY ID DESC) FLOW_END_ID, 
        ROW_NUMBER()OVER(PARTITION BY ACC_NO, BIZ_TYPE ORDER BY ID DESC) RN
        FROM ZF_OPER_ADM.ACCOUNT_FUND_HIS
        WHERE TRANS_TIME &gt;= TO_DATE(#{snapshotDate,jdbcType=VARCHAR}||' 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
        AND TRANS_TIME &lt;= TO_DATE(#{snapshotDate,jdbcType=VARCHAR}||' 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
        ) S WHERE S.RN = 1 ORDER BY S.ID
    </select>

    <insert id="insert" parameterType="com.pay.account.core.entity.AccountSnapshot">
        <selectKey order="BEFORE" keyProperty="id" resultType="long">
            select ZF_OPER_ADM.SEQ_ACCOUNT_SNAPSHOT_ID.nextval from DUAL
        </selectKey>
        insert into ZF_OPER_ADM.ACCOUNT_SNAPSHOT (ID, CREATE_TIME,
        SNAPSHOT_DATE, SNAPSHOT_MONTH, USER_NO,
        USER_ROLE, ACC_NO, ACC_TYPE,
        BIZ_TYPE, PLUS, SUBTRACT,
        PLUS_NUM, SUBTRACT_NUM, PRE_BAL,
        CURR_BAL,FLOW_START_ID,FLOW_END_ID, ORIGIN)
        values (#{id,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP},
        #{snapshotDate,jdbcType=VARCHAR}, #{snapshotMonth,jdbcType=VARCHAR}, #{userNo,jdbcType=VARCHAR},
        #{userRole,jdbcType=VARCHAR}, #{accNo,jdbcType=VARCHAR}, #{accType,jdbcType=VARCHAR},
        #{bizType,jdbcType=VARCHAR}, #{plus,jdbcType=DECIMAL}, #{subtract,jdbcType=DECIMAL},
        #{plusNum,jdbcType=DECIMAL}, #{subtractNum,jdbcType=DECIMAL}, #{preBal,jdbcType=DECIMAL},
        #{currBal,jdbcType=DECIMAL}, #{flowStartId,jdbcType=DECIMAL}, #{flowEndId,jdbcType=DECIMAL},
        #{origin,jdbcType=VARCHAR})
    </insert>
    <delete id="deleteBySnapshotDate">
        delete from ZF_OPER_ADM.ACCOUNT_SNAPSHOT WHERE SNAPSHOT_DATE = #{snapshotDate,jdbcType=VARCHAR}
    </delete>

</mapper>