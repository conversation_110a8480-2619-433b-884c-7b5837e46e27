package com.pay.account.core.dto.rsp.account;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version [版本号, 2018年12月5日 上午10:33:52]
 * @Description 账户资金返回
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Data
public class AccountKeepRes {

    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 账户类型
     */
    private String accType;

    /**
     * 账户编号
     */
    private String accNo;

    /**
     * 记账后的账户余额
     */
    private BigDecimal balance;
}
