package com.pay.account.core.biz.account;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.pay.frame.common.base.enums.account.WithdrawType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.github.pagehelper.PageInfo;
import com.pay.account.core.constants.SysConstants;
import com.pay.account.core.dto.req.account.AccountManageReqBean;
import com.pay.account.core.dto.req.account.AccountUpdatePwdReq;
import com.pay.account.core.dto.rsp.account.AccountManageRes;
import com.pay.account.core.entity.Account;
import com.pay.account.core.entity.AccountManageHis;
import com.pay.account.core.enums.HandlerResult;
import com.pay.account.core.exception.BizRuntimeException;
import com.pay.account.core.service.account.AccountManageHisService;
import com.pay.account.core.service.account.AccountService;
import com.pay.account.core.utils.AccountUtils;
import com.pay.account.core.utils.JsonUtils;
import com.pay.frame.common.base.enums.AccountOperType;
import com.pay.frame.common.base.enums.AccountType;
import com.pay.frame.common.base.enums.Origin;
import com.pay.frame.common.base.enums.account.AccountStatus;
import com.pay.frame.common.base.exception.ServerException;
import com.pay.frame.common.base.util.RandomUtils;
import com.pay.frame.common.base.util.RedisKeyUtil;
import com.pay.frame.common.base.util.StringUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024-02-23 17:32:47
 **/
@Slf4j
@Component
public class AccountManageBiz {
    @Autowired
    private AccountService accountService;
    @Autowired
    private AccountManageHisService accountManageHisService;
    @Autowired
    private RedisTemplate redisTemplate;


    private List<String> ZF_ACC = Arrays.asList(AccountType.PROFIT.name(),
            AccountType.ACTIVE.name(),
            AccountType.SIM.name(),
            AccountType.CASHBACK.name());

    private List<String> LS_ACC = Arrays.asList(AccountType.LS_ACTIVE.name(),
            AccountType.LS_PROFIT.name(),
            AccountType.LS_SIM.name());

    private List<String> YS_ACC = Arrays.asList(AccountType.YS_ACTIVE.name(),
            AccountType.YS_PROFIT.name(),
            AccountType.YS_SIM.name());

    /**
     * 账户全部开户
     */
    @Transactional
    public AccountManageRes openAccAll(List<AccountManageReqBean> openReqList) {
        AccountManageRes rspBean = new AccountManageRes();
        for (AccountManageReqBean openReq : openReqList) {
            rspBean = openAcc(openReq);
        }
        return rspBean;
    }

    /**
     * 账户开户
     */
    @Transactional
    public AccountManageRes openAcc(AccountManageReqBean accReq) {
        AccountManageRes rspBean = new AccountManageRes();
        Account repeatAcc = accountService.findByUserNoAndType(accReq.getUserNo(), accReq.getAccType().name());
        if (null != repeatAcc && !"".equals(repeatAcc.getAccNo())) {
            log.info("账户开户幂等 ：{}", JsonUtils.toJsonString(accReq));
            return rspBean;
        }
        createAccount(accReq);
        return rspBean;
    }

    /**
     * 账户销户
     * 销户的充要条件
     * 1. 账户余额必须是 0
     * 2. 账户状态 NORMAL（正常）
     * 3. 账户类型只支持资金池类（CP_*）
     *
     * @param accReq
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public void cancelAcc(AccountManageReqBean accReq) {
        accReq.setOperType(AccountOperType.CANCEL);
        Account repeatAcc = accountService.findByUserNoAndType(accReq.getUserNo(), accReq.getAccType().name());
        if (repeatAcc.getBalance().compareTo(BigDecimal.ZERO) != 0
                || repeatAcc.getFrzBal().compareTo(BigDecimal.ZERO) != 0) {
            throw new BizRuntimeException("注销账户异常，账户余额不为【0】不能注销。");
        }
        Account lockAcc = accountService.findByAccNoForUpdate(repeatAcc.getAccNo());
        AccountManageHis manageHis = buildAccountManageHis(accReq);
        manageHis.setAccStatus(AccountStatus.CANCEL);
        manageHis.setAccNo(lockAcc.getAccNo());
        accountManageHisService.insert(manageHis);
        lockAcc.setStatus(AccountStatus.CANCEL.name());
        lockAcc.setRemark(accReq.getRemark());
        lockAcc.setUpdateTime(new Date());
        accountService.updateByAccNo(lockAcc);
        log.info("账户注销成功 ：{}, {}, {}", manageHis, lockAcc);
    }

    /**
     * 冻结账户
     *
     * @param accReq
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AccountManageRes freezeAccount(AccountManageReqBean accReq) {
        if (!accReq.getOperType().equals(AccountOperType.FREEZE)) {
            throw new BizRuntimeException(HandlerResult.ACCOUNT_OPERTYPE_ERROR);
        }
        Account lockAcc = accountService.findByAccNoForUpdate(accReq.getAccNo());
        AccountManageHis idempotentHis = accountManageHisService.idempotentCheck(accReq.getAccNo(),
                AccountOperType.FREEZE.name(),
                accReq.getRequestFlow());
        AccountManageRes rspBean = new AccountManageRes();
        rspBean.setUserNo(lockAcc.getUserNo());
        rspBean.setAccType(AccountType.valueOf(lockAcc.getAccType()));
        if (idempotentHis != null) {
            log.info("账户冻结幂等验证 已存在当前操作{}", idempotentHis);
            return rspBean;
        }

        saveManageHis(accReq, lockAcc);
        lockAcc.setStatus(AccountStatus.FREEZE.name());
        lockAcc.setRemark(accReq.getRemark());
        lockAcc.setUpdateTime(new Date());
        lockAcc.setFreezeCount(lockAcc.getFreezeCount() + 1);
        accountService.updateByAccNo(lockAcc);

        log.info("账户冻结成功 ：{}, {}", accReq, rspBean);
        return rspBean;
    }

    /**
     * 解冻账户
     *
     * @param accReq
     * @return
     */
    @Transactional
    public AccountManageRes unFreezeAccount(AccountManageReqBean accReq) {
        if (!accReq.getOperType().equals(AccountOperType.UNFREEZE)) {
            throw new BizRuntimeException(HandlerResult.ACCOUNT_OPERTYPE_ERROR);
        }
        Account lockAcc = accountService.findByAccNoForUpdate(accReq.getAccNo());
        AccountManageHis idempotentHis = accountManageHisService.idempotentCheck(accReq.getAccNo(),
                AccountOperType.UNFREEZE.name(),
                accReq.getRequestFlow());
        AccountManageRes rspBean = new AccountManageRes();
        rspBean.setUserNo(accReq.getUserNo());
        rspBean.setAccType(accReq.getAccType());
        if (idempotentHis != null) {
            log.info("账户解冻幂等验证 已存在当前操作{}", idempotentHis);
            return rspBean;
        }

        AccountManageHis uniqFreeze = accountManageHisService.idempotentCheck(lockAcc.getAccNo(),
                AccountOperType.FREEZE.name(),
                accReq.getRequestFlow());
        if (uniqFreeze == null) {
            log.info("当前流水号无冻结记录");
            throw new BizRuntimeException(HandlerResult.UNFREEZE_NOTFOUND_BY_FLOW);
        }

        saveManageHis(accReq, lockAcc);
        int freezeCount = lockAcc.getFreezeCount() - 1;
        lockAcc.setFreezeCount(freezeCount);
        if (freezeCount == 0) {
            lockAcc.setStatus(AccountStatus.NORMAL.name());
            lockAcc.setRemark(accReq.getRemark());
            lockAcc.setUpdateTime(new Date());
        }
        accountService.updateByAccNo(lockAcc);
        log.info("账户解冻完成 ： {}, {}", accReq, rspBean);
        return rspBean;
    }

    private void saveManageHis(AccountManageReqBean accReq, Account lockAcc) {
        if (StringUtils.isBlank(accReq.getRemark())) {
            accReq.setRemark(SysConstants.ACCOUNT_UNFREEZE_REMARK);
        }
        accReq.setAccType(AccountType.valueOf(lockAcc.getAccType()));
        AccountManageHis manageHis = buildAccountManageHis(accReq);
        manageHis.setAccStatus(AccountStatus.valueOf(lockAcc.getStatus()));
        accountManageHisService.insert(manageHis);
    }

    /**
     * @param accReq 请求入参
     * @Description 创建账户
     * @see [类、类#方法、类#成员]
     */
    private void createAccount(AccountManageReqBean accReq) {
        String accNo = AccountUtils.generateAccNo(accReq.getUserNo(), accReq.getAccType());
        String remark = accReq.getRemark() == null ? SysConstants.ACCOUNT_OPEN_REMARK : accReq.getRemark();
        Account account = new Account();
        Date currDate = new Date();
        String payPwd = AccountUtils.signPwd(accReq.getPayPw(), accReq.getUserNo());
        account.setUserNo(accReq.getUserNo());
        account.setAccType(accReq.getAccType().name());
        account.setPayPw(payPwd);
        account.setAccNo(accNo);
        account.setUserRole(accReq.getUserRole());
        account.setBalance(BigDecimal.ZERO);
        account.setCreateTime(currDate);
        account.setFrzBal(BigDecimal.ZERO);
        account.setUpdateTime(currDate);
        account.setOpenTime(currDate);
        account.setOptimistic(0L);
        account.setRemark(remark);
        account.setSignature(AccountUtils.signature(account));
        account.setStatus(AccountStatus.NORMAL.name());
        account.setFreezeCount(0);
        String origin = accReq.getOrigin();
        account.setOrigin(org.springframework.util.StringUtils.isEmpty(origin) ? getAccOrigin(accReq.getAccType()) : origin);
        AccountManageHis manageHis = buildAccountManageHis(accReq);
        manageHis.setAccNo(accNo);
        manageHis.setAccStatus(AccountStatus.NORMAL);
        accountManageHisService.insert(manageHis);
        accountService.insertAccount(account);
        log.info("账户开户成功 ：{}", account);
    }



    private String getAccOrigin(AccountType accountType) {
        if (ZF_ACC.contains(accountType)) {
            return Origin.ZF.name();
        } else if (YS_ACC.contains(accountType)) {
            return Origin.YS.name();
        } else if (LS_ACC.contains(accountType)) {
            return Origin.LS.name();
        }
        return Origin.ZF.name();
    }

    /**
     * 构建 AccountManageHis
     *
     * @param manageReq
     * @return
     */
    private static AccountManageHis buildAccountManageHis(AccountManageReqBean manageReq) {
        AccountManageHis manageHis = new AccountManageHis();
        manageHis.setCreateTime(new Date());
        manageHis.setOptimistic(0L);
        manageHis.setAccNo(manageReq.getAccNo());
        manageHis.setAccType(manageReq.getAccType());
        manageHis.setOperType(manageReq.getOperType());
        manageHis.setOperator(manageReq.getOperator());
        manageHis.setRemark(manageReq.getRemark());
        manageHis.setRequestFlow(manageReq.getRequestFlow());
        manageHis.setSysSource(manageReq.getSysSource());
        return manageHis;
    }

    @Transactional
    public void setPayPW(AccountUpdatePwdReq accReq) {
        List<Account> accountList = accountService.findUserAccList(accReq.getUserNo());
        if (CollectionUtils.isEmpty(accountList)) {
            log.info("支付密码变更，账户不存在 ：{}", accountList);
            throw new ServerException("账户不存在");
        }
        log.info("支付密码设置start ：{}", accountList);
        accountList.forEach(a -> {
            AccountManageHis manageHis = new AccountManageHis();
            manageHis.setCreateTime(new Date());
            manageHis.setOptimistic(0L);
            manageHis.setAccNo(a.getAccNo());
            manageHis.setAccStatus(AccountStatus.valueOf(a.getStatus()));
            manageHis.setAccType(AccountType.valueOf(a.getAccType()));
            manageHis.setOperType(AccountOperType.SETTPWD);
            manageHis.setOperator(a.getUserNo());
            manageHis.setRemark("设置支付密码");
            manageHis.setRequestFlow(RandomUtils.getFlowNo());
            manageHis.setSysSource("System");
            accountManageHisService.insert(manageHis);
            a.setPayPw(AccountUtils.signPwd(accReq.getPayPwd(), accReq.getUserNo()));
            a.setRemark("设置支付密码");
            a.setUpdateTime(new Date());
            accountService.updateByAccNo(a);
            log.info("支付密码设置end ：{}", a);
        });

        redisTemplate.delete(RedisKeyUtil.pubKey(SysConstants.PAY_PW_ERROR_TIMES, accReq.getUserNo()));
    }

    public PageInfo<AccountManageHis> pageByParams(Map<String, String> params) {
        int currentPage = Integer.valueOf(params.getOrDefault("currentPage", "1"));
        int pageSize = Integer.valueOf(params.getOrDefault("pageSize", "10"));
        return accountManageHisService.pageByParams(params, currentPage, pageSize);
    }

    public PageInfo<AccountManageHis> page4UnFreeze(Map<String, String> params) {
        int currentPage = Integer.valueOf(params.getOrDefault("currentPage", "1"));
        int pageSize = Integer.valueOf(params.getOrDefault("pageSize", "10"));
        return accountManageHisService.page4UnFreeze(params, currentPage, pageSize);
    }

    @Transactional
    public void setUpdateAccPayPW(Account acc) {
        AccountManageHis manageHis = new AccountManageHis();
        manageHis.setCreateTime(new Date());
        manageHis.setOptimistic(0L);
        manageHis.setAccNo(acc.getAccNo());
        manageHis.setAccStatus(AccountStatus.valueOf(acc.getStatus()));
        manageHis.setAccType(AccountType.valueOf(acc.getAccType()));
        manageHis.setOperType(AccountOperType.SETTPWD);
        manageHis.setOperator(acc.getUserNo());
        manageHis.setRemark("设置支付密码");
        manageHis.setRequestFlow(RandomUtils.getFlowNo());
        manageHis.setSysSource("System");
        accountManageHisService.insert(manageHis);
        acc.setRemark("设置支付密码");
        acc.setUpdateTime(new Date());
        accountService.updateByAccNo(acc);
        log.info("支付密码设置end ：{}", acc);
    }
}
