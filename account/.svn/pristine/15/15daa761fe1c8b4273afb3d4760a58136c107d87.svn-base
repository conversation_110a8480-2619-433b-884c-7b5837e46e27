package com.pay.account.core.biz.account;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pay.account.core.biz.remit.WithdrawBiz;
import com.pay.account.core.dto.rsp.account.AccountFundBean;
import com.pay.account.core.dto.rsp.account.AccountFundSumAmtRsp;
import com.pay.account.core.dto.rsp.remit.WithdrawRsp;
import com.pay.account.core.entity.AccountFundHis;
import com.pay.account.core.service.account.AccountFundHisService;
import com.pay.account.core.utils.DateUtil;
import com.pay.frame.common.base.enums.BizType;
import com.pay.frame.common.base.enums.account.WithdrawType;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024-02-26 09:19:17
 **/
@Slf4j
@Component
public class AccountFundBiz {

	@Resource
	private AccountFundHisService accountFundHisService;
	
	@Resource
	private WithdrawBiz withdrawBiz;

	/**
	 * 对象转换包装
	 *
	 * @param accountFundHis
	 * @return
	 */
	public AccountFundBean convAccountFundBean(AccountFundHis accountFundHis) {
		String bizType = accountFundHis.getBizType();
		AccountFundBean accFund = new AccountFundBean();
		BeanUtils.copyProperties(accountFundHis, accFund);
		accFund.setCreateTime(DateUtil.formatDate(accountFundHis.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
		accFund.setRemark(accountFundHis.getRemark());
		accFund.setBizDesc(BizType.valueOf(bizType).getDesc());

		if (bizType.endsWith("WITHDRAW") && !WithdrawType.TRANS_WITHDRAW.name().equals(bizType)) {
			WithdrawRsp withdrawRsp = withdrawBiz.withDrawDetail(accountFundHis.getTransOrder());
			accFund.setAccStatus(withdrawRsp.getStatus());
			accFund.setStatusDesc(withdrawRsp.getStatusDesc());
		}
		return accFund;
	}
	

	public PageInfo<AccountFundBean> pageByParams(Map<String, String> params, int currentPage, int pageSize) {
		PageInfo<AccountFundHis> pageInfo = accountFundHisService.pageByParams(params, currentPage, pageSize);
		PageInfo<AccountFundBean> result = new PageInfo<>();
		BeanUtils.copyProperties(pageInfo, result);
		List<AccountFundBean> fundBeanList = pageInfo.getList().stream().map(this::convAccountFundBean)
				.collect(Collectors.toList());
		result.setList(fundBeanList);
		return result;
	}




	public List<AccountFundBean> queryAccFundPage(Map<String, String> requestMap, int currPage, int pageSize) {
		PageInfo<AccountFundHis> pageInfo = accountFundHisService.pageByParams(requestMap, currPage, pageSize);
		List<AccountFundBean> fundBeanList = pageInfo.getList().stream().map(this::convAccountFundBean)
				.collect(Collectors.toList());
		return fundBeanList;
	}


	public AccountFundSumAmtRsp findAccFundSumAmt(Map<String, String> requestMap) {
		return accountFundHisService.findAccFundSumAmt(requestMap);
	}
}
