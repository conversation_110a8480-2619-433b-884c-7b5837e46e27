package com.pay.account.core.dto.req.account;

import lombok.Data;

/**
 * 积分汇总查询
 *
 * <AUTHOR>
 * @date 2021-11-11 14:30:33
 **/
@Data
public class AccountQueryReq {


    private String userNo;

    private String accType;
    
    private String userRole;

    /**
     * 资金方向 （PLUS/SUBTRACT）
     */
    private String fundSymbol;

    /**
     * 每页数据数
     */
    private int pageSize = 10;

    /**
     * 当前页码
     */
    private int currentPage = 1;
}
