package com.pay.account.channel.zhongfu.client;


import com.pay.account.channel.ChannelInterface;
import com.pay.account.channel.bean.req.*;
import com.pay.account.channel.bean.res.*;
import com.pay.account.channel.zhongfu.assembler.ZFTransAssembler;
import com.pay.account.channel.zhongfu.exception.ZFException;
import com.pay.account.channel.zhongfu.exception.ZFTransException;
import com.pay.account.core.remote.PayChannelClient;
import com.pay.account.core.remote.bean.settle.req.SettleFoRepushInstructReqDto;
import com.pay.account.core.remote.bean.settle.req.SettleInstructFoQueryReqDto;
import com.pay.account.core.remote.bean.settle.res.SettleFoRepushInstructRspDto;
import com.pay.account.core.remote.bean.settle.res.SettleInstructFoQueryRspDto;
import com.pay.frame.common.base.bean.ResultsBean;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
public class ZFTransClient implements ChannelInterface {

    private ZFTransAssembler zfTransAssembler;

    @Resource
    private PayChannelClient payChannelClient;


    public ZFTransClient(ZFTransAssembler zfTransAssembler) {
        this.zfTransAssembler = zfTransAssembler;
    }

    @Override
    public RegisterRes register(RegisterReq registerReq) {
        throw new ZFTransException("98", "非法的操作");
    }

    @Override
    public RemitRes remit(RemitReq remitReq) {
        RemitRes remitRes = zfTransAssembler.toRemitRes(null);
        return remitRes;
    }

    @Override
    public FoRepushRes foRepush(FoRepushReq remitReq) {
        SettleFoRepushInstructReqDto zfFoRepushInstructReqDto = zfTransAssembler.toFoRepush(remitReq);
        log.info("zfTrans foRepush start {} ", zfFoRepushInstructReqDto);
        ResultsBean<SettleFoRepushInstructRspDto> resultsBean = payChannelClient.foRepush(zfFoRepushInstructReqDto);
        log.info("zfTrans foRepush end {}", resultsBean);
        if (resultsBean.notSuccess()) {
            throw new ZFTransException("98", "ZF出款补偿异常" + remitReq.getTransOrder()+" " + resultsBean.getMessage());
        }
        FoRepushRes foRepushRes = zfTransAssembler.toFoRepushRes(resultsBean.getObject());
        return foRepushRes;

    }

    @Override
    public RemitQueryRes remitQuery(RemitQueryReq remitQueryReq) {
        SettleInstructFoQueryReqDto zfSettleFoQueryReqDto = zfTransAssembler.toRemitQuery(remitQueryReq);
        log.info("zfTrans remitQuery start {} ", zfSettleFoQueryReqDto);
        ResultsBean<SettleInstructFoQueryRspDto> result = payChannelClient.foQuery(zfSettleFoQueryReqDto);
        log.info("zfTrans remitQuery end {} ", result);
        if (result.notSuccess()) {
            throw new ZFException("98", "ZF提现结果查询 异常" + remitQueryReq.getChannelTransOrder());
        }
        RemitQueryRes remitQueryRes = zfTransAssembler.toRemitQueryRes(result.getObject());
        return remitQueryRes;
    }

    @Override
    public BalanceRes balanceQuery(BalanceReq balanceReq) {
        throw new ZFTransException("98", "非法的操作");
    }

}
