package com.pay.account.core.remote;

import org.hibernate.validator.constraints.NotBlank;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.constants.CommonConstants;

import lombok.Data;

@RequestMapping(CommonConstants.AUTH_APPLICATION_NAME_CORE)
@FeignClient(value = CommonConstants.AUTH_EUREKA_SERVER_INSTANCE_CORE)
public interface SmsClient {
    /**
     * @Description 发送短信
     */
    @RequestMapping(value = "/sms/sendMsg", method = RequestMethod.POST)
    ResultsBean<String> sendSms(@RequestBody SendParams sendParams);


    @Data
    class SendParams {
        public SendParams() {
        }

        /**
         * 请求号
         */
        private String requestNo;

        /**
         * 消息模板ID
         */
        @NotBlank
        private String templateCode;

        /**
         * 电话号
         */
        private String phoneNo;

        /**
         * 内容
         */
        private String templateParam;
        private String userNo;
    }

}
