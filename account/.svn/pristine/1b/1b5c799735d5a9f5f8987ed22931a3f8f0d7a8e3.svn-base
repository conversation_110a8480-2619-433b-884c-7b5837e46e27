package com.pay.account.core.enums;

import java.math.BigDecimal;

import com.pay.account.core.biz.account.SystemAccountBiz;
import com.pay.account.core.entity.RemitBill;
import com.pay.frame.common.base.enums.AccountType;
import com.pay.frame.common.base.enums.BizType;
import com.pay.frame.common.base.enums.account.FundSymbol;

import lombok.extern.slf4j.Slf4j;

/**
 * 通道支付策略
 *
 * <AUTHOR>
 * @date 2022-04-24 10:35:33
 **/
@Slf4j
public enum ChannelPayableStrategy {

    zf_trans("中付交易付款策略") {
        @Override
        public boolean canPayable(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            return true;
        }

        @Override
        public boolean canRetreat(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            return true;
        }
    },

    zf_marketing("中付营销付款策略") {
        @Override
        public boolean canPayable(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            zfPayable(systemAccountBiz, AccountType.SYS_MARKETING, remitBill, OriginSysUserNo.ZF_ORIGIN.getUserNo());
            return true;
        }

        @Override
        public boolean canRetreat(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            zfRetreat(systemAccountBiz, AccountType.SYS_MARKETING, remitBill, OriginSysUserNo.ZF_ORIGIN.getUserNo());
            return true;
        }
    },

    zf_corp_marketing("中付系统对公营销付款策略") {
        @Override
        public boolean canPayable(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            // zfPayable(systemAccountBiz, AccountType.SYS_MARKETING, remitBill, OriginSysUserNo.ZF_ORIGIN.getUserNo());
            return true;
        }

        @Override
        public boolean canRetreat(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            // zfRetreat(systemAccountBiz, AccountType.SYS_MARKETING, remitBill, OriginSysUserNo.ZF_ORIGIN.getUserNo());
            return true;
        }
    },

    ys_trans("银盛交易付款策略") {
        @Override
        public boolean canPayable(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            return true;
        }

        @Override
        public boolean canRetreat(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            return true;
        }
    },

    ys_marketing("银盛营销付款策略") {
        @Override
        public boolean canPayable(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            ysPayable(systemAccountBiz, AccountType.SYS_MARKETING, remitBill, OriginSysUserNo.YS_ORIGIN.getUserNo());
            return true;
        }

        @Override
        public boolean canRetreat(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            ysRetreat(systemAccountBiz, AccountType.SYS_MARKETING, remitBill, OriginSysUserNo.YS_ORIGIN.getUserNo());
            return true;
        }
    },

    ls_trans("乐刷交易付款策略") {
        @Override
        public boolean canPayable(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            return true;
        }

        @Override
        public boolean canRetreat(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            return true;
        }
    },

    ls_marketing("乐刷营销付款策略") {
        @Override
        public boolean canPayable(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            return true;
        }

        @Override
        public boolean canRetreat(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            return true;
        }
    },

    pyp_marketing("碰一碰营销付款策略") {
        @Override
        public boolean canPayable(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            ysPayable(systemAccountBiz, AccountType.SYS_MARKETING, remitBill, OriginSysUserNo.PYP_ORIGIN.getUserNo());
            return true;
        }

        @Override
        public boolean canRetreat(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            ysRetreat(systemAccountBiz, AccountType.SYS_MARKETING, remitBill, OriginSysUserNo.PYP_ORIGIN.getUserNo());
            return true;
        }
    },

    qixinbao("企薪宝通道付款策略") {
        @Override
        public boolean canPayable(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            return true;
        }

        @Override
        public boolean canRetreat(SystemAccountBiz systemAccountBiz, RemitBill remitBill) {
            return true;
        }
    };

    /**
     * 说明
     */
    private String remark;


    ChannelPayableStrategy(String remark) {
        this.remark = remark;
    }

    public String getRemark() {
        return remark;
    }

    /**
     * 验证通道是否可支付
     *
     * @param remitBill
     * @return
     */
    abstract public boolean canPayable(SystemAccountBiz systemAccountBiz, RemitBill remitBill);

    /**
     * 验证通道是否可退回
     *
     * @param remitBill
     * @return
     */
    abstract public boolean canRetreat(SystemAccountBiz systemAccountBiz, RemitBill remitBill);

    private final static BigDecimal ZF_FEE = new BigDecimal("0.2");
    private final static String ZF_FEE_REMARK = "中付提现手续费";

    private final static BigDecimal YS_FEE = new BigDecimal("1.0");
    private final static String YS_FEE_REMARK = "银盛提现手续费";


    public void zfPayable(SystemAccountBiz systemAccountBiz, AccountType accountType, RemitBill remitBill, String userNo) {
        keepSub(systemAccountBiz,
                accountType,
                remitBill.getRemitAmount(),
                remitBill.getBusinessCode(),
                remitBill.getRemark(),
                remitBill.getTransOrder(),
                userNo);
        keepSub(systemAccountBiz,
                accountType,
                ZF_FEE,
                BizType.WITHDRAW_FEE.name(),
                ZF_FEE_REMARK,
                remitBill.getTransOrder() + "_FEE",
                userNo);
    }

    public void ysPayable(SystemAccountBiz systemAccountBiz, AccountType accountType, RemitBill remitBill, String userNo) {
        keepSub(systemAccountBiz,
                accountType,
                remitBill.getRemitAmount(),
                remitBill.getBusinessCode(),
                remitBill.getRemark(),
                remitBill.getTransOrder(),
                userNo);
        keepSub(systemAccountBiz,
                accountType,
                YS_FEE,
                BizType.WITHDRAW_FEE.name(),
                YS_FEE_REMARK,
                remitBill.getTransOrder() + "_FEE",
                userNo);
    }

    private void keepSub(SystemAccountBiz systemAccountBiz, AccountType accountType, BigDecimal remitAmount,
                         String businessCode, String remark, String transOrder, String userNo) {
        BizType bizType = BizType.valueOf(businessCode);
        systemAccountBiz.checkSystemBalance(remitAmount, accountType, userNo);
        systemAccountBiz.keepAccount(accountType,
                bizType,
                remark,
                transOrder,
                FundSymbol.SUBTRACT,
                remitAmount,
                userNo);
    }

    public void zfRetreat(SystemAccountBiz systemAccountBiz, AccountType accountType, RemitBill remitBill, String userNo) {
        keepPlus(systemAccountBiz,
                accountType,
                remitBill.getRemitAmount(),
                remitBill.getRemark(),
                remitBill.getTransOrder(),
                BizType.WITHDRAW_RETREAT,
                userNo);
        keepPlus(systemAccountBiz,
                accountType,
                ZF_FEE,
                ZF_FEE_REMARK,
                remitBill.getTransOrder() + "_FEE",
                BizType.WITHDRAW_FEE_RETREAT,
                userNo);
    }

    public void ysRetreat(SystemAccountBiz systemAccountBiz, AccountType accountType, RemitBill remitBill, String userNo) {
        keepPlus(systemAccountBiz,
                accountType,
                remitBill.getRemitAmount(),
                remitBill.getRemark(),
                remitBill.getTransOrder(),
                BizType.WITHDRAW_RETREAT,
                userNo);
        keepPlus(systemAccountBiz,
                accountType,
                YS_FEE,
                YS_FEE_REMARK,
                remitBill.getTransOrder() + "_FEE",
                BizType.WITHDRAW_FEE_RETREAT,
                userNo);
    }


    private void keepPlus(SystemAccountBiz systemAccountBiz, AccountType accountType, BigDecimal remitAmount,
                          String remark, String transOrder, BizType bizType, String userNo) {
        systemAccountBiz.keepAccount(accountType,
                bizType,
                remark,
                transOrder,
                FundSymbol.PLUS,
                remitAmount,
                userNo);
    }
}
