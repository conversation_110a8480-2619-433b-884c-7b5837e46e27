<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pay.account.core.mapper.WithdrawConfigMapper">
    <resultMap id="BaseResultMap" type="com.pay.account.core.entity.WithdrawConfig">
        <id column="ID" jdbcType="DECIMAL" property="id"/>
        <result column="OPTIMISTIC" jdbcType="DECIMAL" property="optimistic"/>
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="USER_NO" jdbcType="VARCHAR" property="userNo"/>
        <result column="BUSINESS_CODE" jdbcType="VARCHAR" property="businessCode"/>
        <result column="MIN_LIMIT" jdbcType="DECIMAL" property="minLimit"/>
        <result column="MAX_LIMIT" jdbcType="DECIMAL" property="maxLimit"/>
        <result column="NUM_LIMIT" jdbcType="DECIMAL" property="numLimit"/>
        <result column="RATE" jdbcType="DECIMAL" property="rate"/>
        <result column="FEE_TYPE" jdbcType="VARCHAR" property="feeType"/>
        <result column="OPERATOR" jdbcType="VARCHAR" property="operator"/>
        <result column="REMARK" jdbcType="VARCHAR" property="remark"/>
        <result column="STATUS" jdbcType="VARCHAR" property="status"/>
        <result column="CHANNEL_NO" jdbcType="VARCHAR" property="channelNo"/>
        <result column="EXT_AMT" jdbcType="DECIMAL" property="extAmt"/>
        <result column="TIME_START" jdbcType="VARCHAR" property="timeStart"/>
        <result column="TIME_END" jdbcType="VARCHAR" property="timeEnd"/>
        <result column="WEEK" jdbcType="VARCHAR" property="week"/>
        <result column="SORT" jdbcType="DECIMAL" property="sort"/>
        <result column="CASHBACK_COST" jdbcType="DECIMAL" property="cashbackCost"/>
        <result column="CHANNEL_COST" jdbcType="DECIMAL" property="channelCost"/>
        <result column="ORIGIN" jdbcType="DECIMAL" property="origin"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!-- -->
        ID, OPTIMISTIC, CREATE_TIME, UPDATE_TIME, USER_NO, BUSINESS_CODE, MIN_LIMIT,
        MAX_LIMIT, RATE, FEE_TYPE, OPERATOR, REMARK, STATUS, NUM_LIMIT, CHANNEL_NO, EXT_AMT,
        TIME_START, TIME_END, WEEK, SORT, CASHBACK_COST, CHANNEL_COST,ORIGIN
    </sql>

    <select id="findToSortArticle" resultMap="BaseResultMap">
        select * from
        (select
        <include refid="Base_Column_List"/>
        from ZF_OPER_ADM.WITHDRAW_CONFIG
        where
        STATUS = 'ENABLE'
        AND ORIGIN = #{origin,jdbcType=VARCHAR}
        <if test="userNo != null and userNo != ''">
            AND (USER_NO = #{userNo,jdbcType=VARCHAR} OR USER_NO = 'DEFAULT')
        </if>
        <if test="week != null and week != ''">
            AND ((WEEK = #{week,jdbcType=VARCHAR}
            AND TIME_START &lt;= TO_CHAR(SYSDATE, 'HH24:MI')
            AND TIME_END >= TO_CHAR(SYSDATE, 'HH24:MI') )
            OR WEEK = 'DEFAULT')
        </if>
        <if test="businessCode != null">
            AND (BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR})
        </if>
        ORDER BY
        SORT ASC,USER_NO ASC, WEEK ASC ,ID )
        WHERE ROWNUM = 1
    </select>

    <select id="listByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ZF_OPER_ADM.WITHDRAW_CONFIG
        <where>
            <if test="queryParams.businessCode != null and queryParams.businessCode != '' ">
                and BUSINESS_CODE=#{queryParams.businessCode,jdbcType=VARCHAR}
            </if>
            <if test="queryParams.userNo != null and queryParams.userNo != '' ">
                AND USER_NO = #{queryParams.userNo,jdbcType=VARCHAR}
            </if>
            <if test="queryParams.origin != null and queryParams.origin != '' ">
                AND ORIGIN = #{queryParams.origin,jdbcType=VARCHAR}
            </if>
            <if test="queryParams.week != null and queryParams.week != '' ">
                AND WEEK = #{queryParams.week,jdbcType=VARCHAR}
            </if>
            <if test="queryParams.status != null and queryParams.status != '' ">
                AND STATUS = #{queryParams.status,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY
        SORT ASC,USER_NO ASC, WEEK ASC ,ID
    </select>

    <select id="findById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ZF_OPER_ADM.WITHDRAW_CONFIG
        where ID = #{id,jdbcType=DECIMAL}
    </select>

    <insert id="insert" parameterType="com.pay.account.core.entity.WithdrawConfig">
        <selectKey order="BEFORE" keyProperty="id" resultType="long">
            select ZF_OPER_ADM.SEQ_WITHDRAW_CONFIG_ID.nextval from DUAL
        </selectKey>
        insert into ZF_OPER_ADM.WITHDRAW_CONFIG (ID, OPTIMISTIC, CREATE_TIME,
        UPDATE_TIME, USER_NO,  BUSINESS_CODE, MIN_LIMIT, MAX_LIMIT,
        RATE, FEE_TYPE, OPERATOR, STATUS,NUM_LIMIT,CHANNEL_NO,
        REMARK, EXT_AMT, TIME_START, TIME_END, WEEK,SORT, CASHBACK_COST, CHANNEL_COST,ORIGIN)
        values (#{id,jdbcType=DECIMAL}, #{optimistic,jdbcType=DECIMAL}, SYSDATE,
        #{updateTime,jdbcType=TIMESTAMP}, #{userNo,jdbcType=VARCHAR},
        #{businessCode,jdbcType=VARCHAR}, #{minLimit,jdbcType=DECIMAL}, #{maxLimit,jdbcType=DECIMAL},
        #{rate,jdbcType=DECIMAL}, #{feeType,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR},
        #{status,jdbcType=VARCHAR}, #{numLimit,jdbcType=DECIMAL}, #{channelNo,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR}, #{extAmt,jdbcType=DECIMAL}, #{timeStart,jdbcType=VARCHAR},
        #{timeEnd,jdbcType=VARCHAR}, #{week,jdbcType=VARCHAR}, #{sort,jdbcType=DECIMAL}, 
        #{cashbackCost,jdbcType=DECIMAL}, #{channelCost,jdbcType=DECIMAL},#{origin,jdbcType=DECIMAL})
    </insert>
    <update id="update" parameterType="com.pay.account.core.entity.WithdrawConfig">
        update ZF_OPER_ADM.WITHDRAW_CONFIG
        set OPTIMISTIC    = OPTIMISTIC + 1,
            UPDATE_TIME   = SYSDATE,
            USER_NO       = #{userNo,jdbcType=VARCHAR},
            BUSINESS_CODE = #{businessCode,jdbcType=VARCHAR},
            MIN_LIMIT     = #{minLimit,jdbcType=DECIMAL},
            MAX_LIMIT     = #{maxLimit,jdbcType=DECIMAL},
            NUM_LIMIT     = #{numLimit,jdbcType=DECIMAL},
            RATE          = #{rate,jdbcType=DECIMAL},
            FEE_TYPE      = #{feeType,jdbcType=VARCHAR},
            OPERATOR      = #{operator,jdbcType=VARCHAR},
            STATUS        = #{status,jdbcType=VARCHAR},
            CHANNEL_NO    = #{channelNo,jdbcType=VARCHAR},
            REMARK        = #{remark,jdbcType=VARCHAR},
            EXT_AMT       = #{extAmt,jdbcType=DECIMAL},
            CASHBACK_COST = #{cashbackCost,jdbcType=DECIMAL},
            CHANNEL_COST  = #{channelCost,jdbcType=DECIMAL},
            TIME_START    = #{timeStart,jdbcType=VARCHAR},
            TIME_END      = #{timeEnd,jdbcType=VARCHAR},
            WEEK          = #{week,jdbcType=VARCHAR},
            SORT          = #{sort,jdbcType=VARCHAR},
            ORIGIN          = #{origin,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=DECIMAL}
          and OPTIMISTIC = #{optimistic,jdbcType=DECIMAL}
    </update>

    <update id="updateStatus" parameterType="com.pay.account.core.entity.WithdrawConfig">
        update ZF_OPER_ADM.WITHDRAW_CONFIG
        set OPTIMISTIC  = OPTIMISTIC + 1,
            UPDATE_TIME = SYSDATE,
            STATUS      = #{status,jdbcType=VARCHAR},
            OPERATOR    = #{operator,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=DECIMAL}
          and OPTIMISTIC = #{optimistic,jdbcType=DECIMAL}
    </update>
</mapper>