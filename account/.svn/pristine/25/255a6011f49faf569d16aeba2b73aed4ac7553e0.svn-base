package com.pay.account.channel.qxb.handler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pay.account.channel.qxb.bean.req.QxbAssemblerReq;
import com.pay.account.channel.qxb.bean.req.QxbBaseReq;
import com.pay.account.channel.qxb.bean.res.QxbAssemblerRes;
import com.pay.account.channel.qxb.bean.res.QxbBaseRes;
import com.pay.account.channel.qxb.exception.QxbException;
import com.pay.account.channel.qxb.util.AESTool;
import com.pay.account.channel.qxb.util.ByteUtil;
import com.pay.account.channel.qxb.util.SignTool;
import com.pay.account.channel.qxb.util.codec.Base64;
import com.pay.account.core.exception.BizRuntimeException;
import com.pay.frame.common.base.util.JsonUtils;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.client.RestTemplate;

/**
 * 企薪宝
 *
 * <AUTHOR>
 *
 */
@Slf4j
@Builder
public class QxbChannelHandler {

    private RestTemplate qxbRestTemplate;

    private String gatewayUrl;

    private String aesKeyStr;

    private String appId;

    private String appPrivateKeyStr;

    private String zmPublicKeyStr;


    public <T> QxbBaseRes<T> handle(QxbBaseReq req, TypeReference<QxbBaseRes<T>> typeReference) {
        try {
            StopWatch sw = new StopWatch("请求企薪宝通道: " + req.getMethod());
            sw.start("sign");
            log.info("企薪宝请求对象 {}, {}", gatewayUrl, JsonUtils.bean2Json(req));
            QxbAssemblerReq qxbAssemblerReq = signData(req);
            sw.stop();
            sw.start("http");
            log.info("请求企薪宝请求开始 【request: {}】", qxbAssemblerReq);
            String response = qxbRestTemplate.postForObject(gatewayUrl, qxbAssemblerReq, String.class);
            sw.stop();
            log.info("请求企薪宝响应原始数据 【response: {}】", response);
            sw.start("parse");
            QxbBaseRes<T> qxbRes = parseData(response, typeReference);
            sw.stop();
            log.info("请求企薪宝响应解析数据 【qxbRes: {}】,【cost: {}】", JsonUtils.bean2Json(qxbRes), sw.prettyPrint());
            return qxbRes;
        } catch (Exception e) {
            log.error("企薪宝调用接口异常 {}, {} ", req, req.getMethod(), e);
            throw new QxbException("98", "通道报备异常", e);
        }
    }

    private QxbAssemblerReq signData(QxbBaseReq req) {
        try {
            String message = JsonUtils.bean2Json(req);
            String appAesKey = Base64.encodeBase64String(ByteUtil.hex2byte(aesKeyStr));
            String encodeRSABase64 = AESTool.getInstance().encrypt(message, "AES", appAesKey, "utf-8");
            String signRSABase64 = SignTool.getInstance().rsa256Sign(message, appPrivateKeyStr, "utf-8");
            QxbAssemblerReq qxbAssemblerReq = new QxbAssemblerReq();
            qxbAssemblerReq.setCrypt(encodeRSABase64);
            qxbAssemblerReq.setSigna(signRSABase64);
            qxbAssemblerReq.setAppid(appId);
            return qxbAssemblerReq;
        } catch (Exception e) {
            log.error("企薪宝签名验证异常: {}", req, e);
            throw new QxbException("98", "通道签名验证异常", e);
        }
    }

    private <T> QxbBaseRes<T> parseData(String response, TypeReference<QxbBaseRes<T>> typeReference) {
        try {
            QxbAssemblerRes qxbAssemblerRes = JsonUtils.json2Bean(response, QxbAssemblerRes.class);
            String appAesKey = Base64.encodeBase64String(ByteUtil.hex2byte(aesKeyStr));
            String message = AESTool.getInstance().decrypt(qxbAssemblerRes.getCrypt(), "AES", appAesKey, "utf-8");
            message = StringUtils.trimToEmpty(message);
            log.info("企薪宝解析数据结果{}", message);
            Boolean validSignFlag =
                    SignTool.getInstance().rsa256Verify(message, qxbAssemblerRes.getSigna(), zmPublicKeyStr, "utf-8");
            if (!validSignFlag) {
                throw new BizRuntimeException("企薪宝解析异常 ");
            }
            QxbBaseRes<T> res = JsonUtils.parser(message, typeReference);
            return res;
        } catch (Exception e) {
            log.error("企薪宝解析数据结果异常: {}", response, e);
            throw new QxbException("98", "通道报备解析数据结果异常 ", e);
        }
    }
}
