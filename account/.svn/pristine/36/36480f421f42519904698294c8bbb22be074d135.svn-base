package com.pay.account.core.remote;


import com.pay.account.core.remote.bean.proxyPay.req.AcsBalanceQryReqDto;
import com.pay.account.core.remote.bean.proxyPay.req.FundoutProxyQryReqDto;
import com.pay.account.core.remote.bean.proxyPay.req.FundoutProxyReqDto;
import com.pay.account.core.remote.bean.proxyPay.res.AcsBalanceQryRspDto;
import com.pay.account.core.remote.bean.proxyPay.res.FundoutProxyQryRspDto;
import com.pay.account.core.remote.bean.proxyPay.res.FundoutProxyRspDto;
import com.pay.account.core.remote.bean.settle.req.*;
import com.pay.account.core.remote.bean.settle.res.*;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.constants.CommonConstants;
import org.springframework.cloud.netflix.feign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(value = CommonConstants.PAYCHANNEL_EUREKA_SERVER_INSTANCE_CORE)
public interface PayChannelClient {

    @RequestMapping(value = CommonConstants.PAYCHANNEL_APPLICATION_NAME_CORE + "/exports/proxyPay/fundoutProxy")
    public ResultsBean<FundoutProxyRspDto> fundoutProxy(@RequestBody FundoutProxyReqDto reqDto);

    @RequestMapping(value = CommonConstants.PAYCHANNEL_APPLICATION_NAME_CORE + "/exports/proxyPay/fundoutProxyQry")
    public ResultsBean<FundoutProxyQryRspDto> fundoutProxyQry(@RequestBody FundoutProxyQryReqDto reqDto);

    @RequestMapping(value = CommonConstants.PAYCHANNEL_APPLICATION_NAME_CORE + "/exports/settle/settleRemit", method = RequestMethod.POST)
    public ResultsBean<SettleInstructRspDto> settleRemit(@RequestBody SettleInstructReqDto reqDto);

    @RequestMapping(value = CommonConstants.PAYCHANNEL_APPLICATION_NAME_CORE + "/exports/settle/foQuery", method = RequestMethod.POST)
    public ResultsBean<SettleInstructFoQueryRspDto> foQuery(@RequestBody SettleInstructFoQueryReqDto reqDto);

    @RequestMapping(value = CommonConstants.PAYCHANNEL_APPLICATION_NAME_CORE + "/exports/proxyPay/acsBalanceQry", method = RequestMethod.POST)
    public ResultsBean<AcsBalanceQryRspDto> acsBalanceQry(@RequestBody @Validated AcsBalanceQryReqDto reqDto);

    @RequestMapping(value = CommonConstants.PAYCHANNEL_APPLICATION_NAME_CORE + "/exports/settle/foRepush", method = RequestMethod.POST)
    public ResultsBean<SettleFoRepushInstructRspDto> foRepush(@RequestBody SettleFoRepushInstructReqDto reqDto);

    /**
     * 乐刷-银盛-结算查询
     *
     * @param reqDto
     * @return
     */
    @RequestMapping(value = CommonConstants.PAYCHANNEL_APPLICATION_NAME_CORE + "/exports/settle/settleTransQuery", method = RequestMethod.POST)
    public ResultsBean<SettleTransQueryRspDto> settleTransQuery(@RequestBody SettleTransQueryReqDto reqDto);

    @RequestMapping(value = CommonConstants.PAYCHANNEL_APPLICATION_NAME_CORE + "/exports/settle/settleCustQuery", method = RequestMethod.POST)
    public ResultsBean<SettleCustQueryRspDto> settleCustQuery(@RequestBody SettleCustQueryReqDto reqDto);

    @RequestMapping(value = CommonConstants.PAYCHANNEL_APPLICATION_NAME_CORE + "/exports/settle/settleCustRemit", method = RequestMethod.POST)
    public ResultsBean<SettleCustRemitRspDto> settleCustRemit(@RequestBody SettleCustRemitReqDto reqDto);


}
