package com.pay.account.core.controller.account;

import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.pay.account.core.biz.account.AccountBiz;
import com.pay.account.core.constants.SysConstants;
import com.pay.account.core.dto.req.account.AccountQueryReq;
import com.pay.account.core.dto.rsp.account.AccountQueryRspBean;
import com.pay.account.core.entity.Account;
import com.pay.frame.common.base.bean.ResultsBean;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version [版本号, 2018年12月3日 下午9:27:35]
 * @Description 账户查询服务
 * @see [相关类/方法]
 * @since [产品/模块版本]
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/account")
public class AccountController {

    @Resource
    private AccountBiz accountBiz;

    /**
     * 账户信息查询
     *
     * @param queryReq
     * @return
     */
    @RequestMapping(value = "/findAccInfo", method = RequestMethod.POST)
    public ResultsBean<AccountQueryRspBean> queryAccInfo(@RequestBody @Validated AccountQueryReq queryReq) {
        log.info("账户信息查询操作findAccInfo start:{}", queryReq);
        AccountQueryRspBean rspBean = accountBiz.findAccountInfo(queryReq);
        log.info("账户信息查询操作findAccInfo end :{}", rspBean);
        return ResultsBean.SUCCESS(rspBean);
    }

    /**
     * 根据账户编号查询
     *
     * @param accNo
     * @return
     */
    @RequestMapping(value = "/findByAccNo", method = RequestMethod.POST)
    public ResultsBean<Account> findByAccNo(@RequestParam("accNo") String accNo) {
        log.info("根据账户编号查询findByAccNo start:{}", accNo);
        Account rspBean = accountBiz.findByAccNo(accNo);
        log.info("根据账户编号查询findByAccNo end :{}", rspBean);
        return ResultsBean.SUCCESS(rspBean);
    }


    /**
     * 查询账户列表
     *
     * @param requestMap
     * @return
     */
    @RequestMapping(value = "/pageByParams", method = RequestMethod.POST)
    public ResultsBean<PageInfo<Account>> pageByParams(@RequestBody Map<String, String> requestMap) {
        log.info("查询账户信息列表参数:{}", requestMap);
        int currentPage = StringUtils.isBlank(requestMap.get("currentPage")) ? 1 : Integer.parseInt(requestMap.get("currentPage"));
        int pageSize = StringUtils.isBlank(requestMap.get("pageSize")) ? SysConstants.PAGE_SIZE : Integer.parseInt(requestMap.get("pageSize"));
        PageInfo<Account> retObj = accountBiz.pageByParams(requestMap, currentPage, pageSize);
        log.info("查询账户信息列表结果:{}", retObj);
        return ResultsBean.SUCCESS(retObj);
    }

}
