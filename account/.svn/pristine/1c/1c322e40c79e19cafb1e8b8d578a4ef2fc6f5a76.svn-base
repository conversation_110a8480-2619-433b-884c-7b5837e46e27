package com.pay.account.channel.yinsheng.client;


import com.pay.account.channel.ChannelInterface;
import com.pay.account.channel.bean.req.*;
import com.pay.account.channel.bean.res.*;
import com.pay.account.channel.yinsheng.assembler.YSAssembler;
import com.pay.account.channel.yinsheng.exception.YSException;
import com.pay.account.core.remote.PayChannelClient;
import com.pay.account.core.remote.bean.proxyPay.req.FundoutProxyQryReqDto;
import com.pay.account.core.remote.bean.proxyPay.req.FundoutProxyReqDto;
import com.pay.account.core.remote.bean.proxyPay.res.FundoutProxyQryRspDto;
import com.pay.account.core.remote.bean.proxyPay.res.FundoutProxyRspDto;
import com.pay.frame.common.base.bean.ResultsBean;
import com.pay.frame.common.base.enums.RemitStatus;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
public class YSClient implements ChannelInterface {

    @Resource
    private PayChannelClient payChannelClient;

    private YSAssembler YSAssembler;

    public YSClient(YSAssembler YSAssembler) {
        this.YSAssembler = YSAssembler;
    }

    @Override
    public RegisterRes register(RegisterReq registerReq) {
        throw new YSException("98", "非法的操作");
    }

    @Override
    public RemitRes remit(RemitReq remitReq) {
        FundoutProxyReqDto zfAsseYSPayFundoutProxyReqDto = YSAssembler.toRemitReq(remitReq);
        log.info("ys remit start {} ", zfAsseYSPayFundoutProxyReqDto);
        ResultsBean<FundoutProxyRspDto> resultsBean = payChannelClient.fundoutProxy(zfAsseYSPayFundoutProxyReqDto);
        log.info("ys remit end {}", resultsBean);
        if (resultsBean.notSuccess()) {
            throw new YSException("98", "YS提现异常" + remitReq.getChannelTransOrder());
        }
        RemitRes remitRes = YSAssembler.toRemitRes(resultsBean.getObject());
        return remitRes;
    }

    @Override
    public FoRepushRes foRepush(FoRepushReq remitReq) {
        throw new YSException("98", "非法的操作");
    }

    @Override
    public RemitQueryRes remitQuery(RemitQueryReq remitQueryReq) {
        FundoutProxyQryReqDto fundoutProxyQryReqDto = YSAssembler.toRemitQuery(remitQueryReq);
        log.info("ys remitQuery start {} ", fundoutProxyQryReqDto);
        ResultsBean<FundoutProxyQryRspDto> result = payChannelClient.fundoutProxyQry(fundoutProxyQryReqDto);
        log.info("ys remitQuery end {} ", result);
        if (result.notSuccess()) {
            FundoutProxyQryRspDto fundoutProxyQryRspDto = new FundoutProxyQryRspDto();
            fundoutProxyQryRspDto.setPayStatus(RemitStatus.DOING.name());
            fundoutProxyQryRspDto.setMessage(result.getMessage());
            fundoutProxyQryRspDto.setCode(result.getFailCode());
            result.setObject(fundoutProxyQryRspDto);
        }
        RemitQueryRes remitQueryRes = YSAssembler.toRemitQueryRes(result.getObject());
        return remitQueryRes;
    }

    @Override
    public BalanceRes balanceQuery(BalanceReq balanceReq) {
        throw new YSException("98", "非法的操作");
    }

}
