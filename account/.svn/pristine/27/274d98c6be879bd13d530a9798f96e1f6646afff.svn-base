package com.pay.account.channel.leshua.config;

import com.pay.account.channel.leshua.assembler.LSAssembler;
import com.pay.account.channel.leshua.client.LSClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023-03-08 17:37:09
 **/
@Slf4j
@Configuration
public class LSConfig {

    @Bean("lsMktClient")
    public LSClient lsMktClient() {
        return new LSClient(lsMktAssembler());
    }

    @Bean("lsProfitClient")
    public LSClient lsProfitClient() {
        return new LSClient(lsProfitAssembler());
    }

    private LSAssembler lsProfitAssembler() {
        LSAssembler lsAssembler = new LSAssembler();
        lsAssembler.setAcsType("PROFIT");
        return lsAssembler;
    }

    private LSAssembler lsMktAssembler() {
        LSAssembler lsAssembler = new LSAssembler();
        lsAssembler.setAcsType("PAYMENT");
        return lsAssembler;
    }

}
