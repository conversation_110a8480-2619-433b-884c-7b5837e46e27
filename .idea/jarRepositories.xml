<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="RemoteRepositoriesConfiguration">
    <remote-repository>
      <option name="id" value="umpay" />
      <option name="name" value="umpay" />
      <option name="url" value="http://10.8.32.24:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="maven2" />
      <option name="name" value="maven2" />
      <option name="url" value="http://10.8.32.24:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="snapshots" />
      <option name="name" value="snapshots" />
      <option name="url" value="http://10.8.32.24:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus-releases" />
      <option name="name" value="nexus-releases" />
      <option name="url" value="http://10.8.22.22:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://10.8.32.24:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus-snapshots" />
      <option name="name" value="nexus-snapshots" />
      <option name="url" value="http://10.8.22.22:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="releases" />
      <option name="name" value="releases" />
      <option name="url" value="http://10.8.32.24:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Maven Central repository" />
      <option name="url" value="https://repo1.maven.org/maven2" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus-snapshots" />
      <option name="name" value="nexus-snapshots" />
      <option name="url" value="http://10.8.32.24:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="central" />
      <option name="name" value="Central Repository" />
      <option name="url" value="http://10.8.22.22:8081/repository/maven-public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="jboss.community" />
      <option name="name" value="JBoss Community repository" />
      <option name="url" value="https://repository.jboss.org/nexus/content/repositories/public/" />
    </remote-repository>
    <remote-repository>
      <option name="id" value="nexus-releases" />
      <option name="name" value="nexus-releases" />
      <option name="url" value="http://10.8.32.24:8081/repository/maven-public/" />
    </remote-repository>
  </component>
</project>