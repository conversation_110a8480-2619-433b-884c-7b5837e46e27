<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/account/account-common/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/account/account-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/account/account-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/account/account-common/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/account/account-core/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/account/account-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/account/account-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/account/account-core/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/account/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/account/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/account/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/account/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/brand-channel/brand-channel-core/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/brand-channel/brand-channel-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/brand-channel/brand-channel-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/brand-channel/brand-channel-core/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data-sync/manage-sync/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data-sync/manage-sync/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data-sync/manage-sync/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data-sync/manage-sync/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data-sync/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data-sync/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data-sync/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data-sync/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data-sync/trade-sync/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data-sync/trade-sync/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data-sync/trade-sync/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/data-sync/trade-sync/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-frame/pay-frame-cache/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-frame/pay-frame-cache/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-frame/pay-frame-cache/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-frame/pay-frame-cache/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-frame/pay-frame-common/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-frame/pay-frame-common/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-frame/pay-frame-common/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-frame/pay-frame-common/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-frame/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-frame/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-frame/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-frame/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/pay-portal/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/paychannel/lib" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/paychannel/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/paychannel/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/paychannel/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/paychannel/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/settle/settle-core/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/settle/settle-core/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/settle/settle-core/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/settle/settle-core/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/settle/settle-fee/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/settle/settle-fee/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/settle/settle-fee/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/settle/settle-fee/src/main/webapp" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/settle/src/main/dev" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/settle/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/settle/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/settle/src/main/webapp" charset="UTF-8" />
  </component>
</project>